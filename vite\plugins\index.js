import vue from '@vitejs/plugin-vue';

// import Components from 'unplugin-vue-components/vite';
// import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';

import createAutoImport from './auto-import';
import createSvgIcon from './svg-icon';
import createCompression from './compression';
import createSetupExtend from './setup-extend';
import compressPlugin from 'vite-plugin-compression';
import VueDevTools from 'vite-plugin-vue-devtools';
import monacoEditorPlugin from 'vite-plugin-monaco-editor';

import { webUpdateNotice } from '@plugin-web-update-notification/vite';
// import legacyPlugin from '@vitejs/plugin-legacy';

import svgLoader from 'vite-svg-loader';
import vitePluginBundleObfuscator from 'vite-plugin-bundle-obfuscator'; // 引入组件
export default function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [vue()];

  //   vitePlugins.push(
  //     Components({
  //       // allow auto load markdown components under `./src/components/`
  //       extensions: ['vue', 'md'],
  //       // allow auto import and register components used in markdown
  //       include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
  //       resolvers: [
  //         ElementPlusResolver({
  //           importStyle: 'sass',
  //         }),
  //       ],
  //       // dts: 'src/components.d.ts',
  //     }),
  //   );

  vitePlugins.push(createAutoImport());
  vitePlugins.push(createSetupExtend());
  vitePlugins.push(createSvgIcon(isBuild));

  // 添加 VueDevTools 插件
  vitePlugins.push(
    VueDevTools({
      launchEditor: ['webstorm', 'code'],
    }),
  );
  // 添加 svg 插件
  vitePlugins.push(svgLoader());

  vitePlugins.push(
    vitePluginBundleObfuscator({
    // 保持之前的排除规则
    excludes: [
      /codemirror/i,
      /vue-codemirror/i,
      // 明确排除您自己的组件，这是一个重要的测试步骤
      'src/components/Codemirror/index.vue', 
    ],
    // 仅开启最基础的压缩和简化
    options: {
      compact: true,
      simplify: true,

      // --- 暂时禁用所有高风险或复杂选项 ---
      controlFlowFlattening: false,
      deadCodeInjection: false,
      debugProtection: false,
      disableConsoleOutput: false,
      selfDefending: false,
      stringArray: false, // 再次确认此项为 false
      renameGlobals: false,
      // ... 将其他类似的高级转换选项都设置为 false 或其默认的禁用值
    },
    // 其他配置保持不变
    enable: true,
    log: true,
    autoExcludeNodeModules: false,
    threadPool: false,
  }),
  );
  // legacyPlugin 插件
  //   vitePlugins.push(
  //     legacyPlugin({
  //       targets: [
  //         'chrome >=51',
  //         'firefox 52',
  //         'edge >= 15',
  //         'safari >= 9',
  //         'opera >= 36',
  //         'ios >= 9',
  //         'defaults',
  //         'ie >= 11',
  //       ],
  //       additionalLegacyPolyfills: ['regenerator-runtime/runtime'], // 面向 IE11 时需要此插件
  //       modernPolyfills: true,
  //       renderLegacyChunks: true,
  //     }),
  //   );

  // 添加 monacoEditorPlugin 插件
  vitePlugins.push(
    monacoEditorPlugin({
      languages: [
        'javascript',
        'typescript',
        'html',
        'css',
        'json',
        'java',
        'sql',
        'groovy',
        'shell',
        'python',
        'xml',
      ],
    }),
  );

  // 添加 webUpdateNotice 插件
  vitePlugins.push(
    webUpdateNotice({
      logVersion: true,
    }),
  );

  vitePlugins.push(
    compressPlugin({
      ext: '.gz',
      algorithm: 'gzip',
      deleteOriginFile: false,
    }),
  );

  isBuild && vitePlugins.push(...createCompression(viteEnv));

  return vitePlugins;
}
