import * as monaco from 'monaco-editor';
import prettier from 'prettier/standalone';
import parserHtml from 'prettier/parser-html';
import parseCss from 'prettier/parser-postcss';
import parserBabel from 'prettier/parser-babel';
import javaPlugin from 'prettier-plugin-java';
import xmlPlugin from '@prettier/plugin-xml'; // XML 格式化
// import { format } from 'sql-formatter'; // SQL 格式化
import { toRaw } from 'vue';
/**
 * @description 添加格式化prettier动作
 * @param editorRef 编辑器实例
 * @param cb 回调函数
 */
export function setPrettierAction(editorRef, cb) {
  console.log('设置格式化动作，编辑器实例:', editorRef);

  editorRef?.addAction({
    id: 'format-code-prettier',
    label: '格式化代码',
    keybindings: [monaco.KeyMod.Shift | monaco.KeyMod.Alt | monaco.KeyCode.KeyF],
    precondition: null,
    contextMenuGroupId: 'custom',
    contextMenuOrder: 1,
    async run(ed) {
      try {
        console.log('格式化动作被触发');

        // 动态获取当前语言
        const currentLanguage = ed.getModel().getLanguageId();
        const currentValue = toRaw(ed).getValue();

        console.log('当前语言:', currentLanguage);
        console.log('代码长度:', currentValue.length);

        const newValue = await formatString(currentValue, currentLanguage);

        console.log('格式化结果:', newValue === currentValue ? '无变化' : '已更新');

        if (newValue !== currentValue) {
          console.log('调用回调函数更新内容');
          cb(newValue, currentValue, editorRef);
        } else {
          console.log('代码已经是格式化状态');
        }
      } catch (error) {
        console.error('格式化过程中出错:', error);
      }
    },
  });
}

export const prettierConfig = {
  semi: false,
  singleQuote: true,
  printWidth: 120,
  trailingComma: 'none',
  endOfLine: 'auto',
  tabWidth: 2,
};

const formatJavaScript = (string) =>
  prettier.format(string, {
    parser: 'javascript',
    plugins: [parserBabel],
    trailingComma: 'es5',
    ...prettierConfig,
  });

const formatJson = (string) =>
  prettier.format(string, {
    parser: 'json',
    plugins: [parserBabel],
    trailingComma: 'es5',
    ...prettierConfig,
  });

const formatHtml = (string) =>
  prettier.format(string, {
    parser: 'html',
    plugins: [parserBabel, parserHtml],
    ...prettierConfig,
  });

const formatCss = (string) =>
  prettier.format(string, {
    parser: 'css',
    plugins: [parseCss],
    ...prettierConfig,
  });

/**
 * @description 格式化代码工具
 * @created 2025/07/21 09:22:42
 * @param code 编辑器内容数据
 */

const formatCode = async (code) => {
  const formattedCode = await prettier.format(code, {
    parser: 'java',
    plugins: [javaPlugin],
  });

  return formattedCode;
};

const formatterMap = {
  json: formatJson,
  typescript: formatJavaScript,
  javascript: formatJavaScript,
  html: formatHtml,
  css: formatCss,
  java: formatCode,
};

/**
 * 异步格式化字符串，避免阻塞主线程
 * @param {string} str - 要格式化的代码字符串
 * @param {string} language - 编程语言
 * @param {Object} options - 格式化选项
 * @returns {Promise<string>} 格式化后的代码
 */
export const formatString = async (str, language, options = {}) => {
  console.log(language);
  // 检查字符串长度，避免格式化过大的文件
  const maxLength = options.maxLength || 100000; // 默认最大10万字符
  if (str.length > maxLength) {
    throw new Error(`文件过大，超过 ${maxLength} 字符限制`);
  }
  const formatter = formatterMap[language] || formatJson;
  let result = str;
  try {
    result = formatter(str);
  } catch (error) {
    const printer = console;
    printer.log(error);
  }

  return result;
};

/**
 * XML格式化函数，优先使用插件，失败时降级到手写方法
 * @param {string} xml - XML字符串
 * @returns {string} 格式化后的XML
 */
const formatXML = (xml) => {
  try {
    // 尝试使用 Prettier XML 插件
    return prettier.format(xml, {
      parser: 'xml',
      plugins: [xmlPlugin],
      xmlWhitespaceSensitivity: 'ignore',
      printWidth: 80,
      tabWidth: 2,
    });
  } catch (error) {
    console.warn('Prettier XML插件格式化失败，使用手写方法:', error.message);
    // 降级到手写的XML格式化方法
    return formatXMLSimple(xml);
  }
};

/**
 * SQL格式化函数，优先使用插件，失败时降级到手写方法
 * @param {string} sql - SQL字符串
 * @returns {string} 格式化后的SQL
 */
const formatSQL = (sql) => {
  try {
    // 尝试使用 sql-formatter 插件
    return format(sql, {
      language: 'sql',
      tabWidth: 2,
      keywordCase: 'upper',
      functionCase: 'upper',
      dataTypeCase: 'upper',
      linesBetweenQueries: 2,
    });
  } catch (error) {
    console.warn('SQL formatter插件格式化失败，使用手写方法:', error.message);
    // 降级到手写的SQL格式化方法
    return formatSQLSimple(sql);
  }
};

/**
 * 简单的SQL格式化函数，避免依赖重型库
 * @param {string} sql - SQL字符串
 * @returns {string} 格式化后的SQL
 */
const formatSQLSimple = (sql) => {
  // 移除多余的空白字符
  let formatted = sql.replace(/\s+/g, ' ').trim();

  // SQL关键字列表
  const keywords = [
    'SELECT',
    'FROM',
    'WHERE',
    'JOIN',
    'INNER JOIN',
    'LEFT JOIN',
    'RIGHT JOIN',
    'FULL JOIN',
    'ON',
    'GROUP BY',
    'ORDER BY',
    'HAVING',
    'LIMIT',
    'OFFSET',
    'UNION',
    'UNION ALL',
    'INSERT INTO',
    'VALUES',
    'UPDATE',
    'SET',
    'DELETE FROM',
    'CREATE TABLE',
    'ALTER TABLE',
    'DROP TABLE',
    'CREATE INDEX',
    'DROP INDEX',
    'AND',
    'OR',
    'NOT',
    'IN',
    'EXISTS',
    'BETWEEN',
    'LIKE',
    'IS NULL',
    'IS NOT NULL',
    'AS',
    'DISTINCT',
    'COUNT',
    'SUM',
    'AVG',
    'MAX',
    'MIN',
    'CASE',
    'WHEN',
    'THEN',
    'ELSE',
    'END',
  ];

  // 将关键字转换为大写并添加换行
  keywords.forEach((keyword) => {
    const regex = new RegExp(`\\b${keyword.replace(/\s+/g, '\\s+')}\\b`, 'gi');
    formatted = formatted.replace(regex, (match) => {
      if (['AND', 'OR', 'ON'].includes(keyword)) {
        return `\n  ${keyword}`;
      } else if (['SELECT', 'FROM', 'WHERE', 'GROUP BY', 'ORDER BY', 'HAVING'].includes(keyword)) {
        return `\n${keyword}`;
      } else {
        return keyword;
      }
    });
  });

  // 清理多余的空行和缩进
  formatted = formatted
    .split('\n')
    .map((line) => line.trim())
    .filter((line) => line.length > 0)
    .join('\n');

  return formatted;
};

/**
 * 简单的XML格式化函数，避免依赖重型库
 * @param {string} xml - XML字符串
 * @returns {string} 格式化后的XML
 */
const formatXMLSimple = (xml) => {
  const PADDING = '  '; // 缩进字符
  const reg = /(>)(<)(\/*)/g;
  let formatted = xml.replace(reg, '$1\r\n$2$3');
  let pad = 0;

  return formatted
    .split('\r\n')
    .map((node) => {
      let indent = 0;
      if (node.match(/.+<\/\w[^>]*>$/)) {
        indent = 0;
      } else if (node.match(/^<\/\w/)) {
        if (pad !== 0) {
          pad -= 1;
        }
      } else if (node.match(/^<\w[^>]*[^\/]>.*$/)) {
        indent = 1;
      } else {
        indent = 0;
      }

      const padding = PADDING.repeat(pad);
      pad += indent;

      return padding + node;
    })
    .join('\r\n');
};

// 添加XML格式化到映射表
formatterMap.xml = formatXML;
formatterMap.sql = formatSQL;
