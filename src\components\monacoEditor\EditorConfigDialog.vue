<template>
  <el-dialog
    title="编辑器配置"
    v-model="dialogVisible"
    width="600px"
    :before-close="handleClose"
    append-to-body
  >
    <el-form :model="configForm" label-width="120px">
      <el-form-item label="语言">
        <el-select v-model="configForm.language" placeholder="选择语言">
          <el-option
            v-for="lang in supportedLanguages"
            :key="lang.value"
            :label="lang.label"
            :value="lang.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="主题">
        <el-select v-model="configForm.theme" placeholder="选择主题">
          <el-option label="暗色主题" value="vs-dark" />
          <el-option label="亮色主题" value="vs" />
          <el-option label="高对比度" value="hc-black" />
        </el-select>
      </el-form-item>

      <el-form-item label="只读模式">
        <el-switch v-model="configForm.readOnly" />
      </el-form-item>

      <el-form-item label="显示小地图">
        <el-switch v-model="configForm.minimap.enabled" />
      </el-form-item>

      <el-form-item label="自动格式化粘贴">
        <el-switch v-model="configForm.formatOnPaste" />
      </el-form-item>

      <el-form-item label="鼠标滚轮缩放">
        <el-switch v-model="configForm.mouseWheelZoom" />
      </el-form-item>

      <el-form-item label="代码折叠">
        <el-switch v-model="configForm.folding" />
      </el-form-item>

      <el-form-item label="自动闭合括号">
        <el-select v-model="configForm.autoClosingBrackets" placeholder="选择模式">
          <el-option label="总是" value="always" />
          <el-option label="从不" value="never" />
          <el-option label="在前面有文本时" value="beforeWhitespace" />
        </el-select>
      </el-form-item>

      <el-form-item label="自动闭合引号">
        <el-select v-model="configForm.autoClosingQuotes" placeholder="选择模式">
          <el-option label="总是" value="always" />
          <el-option label="从不" value="never" />
          <el-option label="在前面有文本时" value="beforeWhitespace" />
        </el-select>
      </el-form-item>

      <!-- 编辑器UI配置 -->
      <el-form-item label="编辑器高度">
        <el-input-number
          v-model="configForm.ui.height"
          :min="200"
          :max="1000"
          :step="50"
          placeholder="编辑器高度(px)"
        />
      </el-form-item>

      <el-form-item label="编辑器字体">
        <el-input
          v-model="configForm.fontFamily"
          placeholder="字体名称，如: Consolas, 'Courier New', monospace"
        />
      </el-form-item>

      <el-form-item label="字体大小">
        <el-input-number
          v-model="configForm.fontSize"
          :min="10"
          :max="24"
          :step="1"
          placeholder="字体大小"
        />
      </el-form-item>

      <!-- 右键菜单配置 -->
      <el-form-item label="启用自定义菜单">
        <el-switch v-model="configForm.contextMenu.enableCustomMenu" />
      </el-form-item>

      <el-form-item label="隐藏默认菜单">
        <el-switch v-model="configForm.contextMenu.hideDefaultMenu" />
      </el-form-item>

      <el-form-item label="启用格式化">
        <el-switch v-model="configForm.contextMenu.enableFormatter" />
      </el-form-item>

      <el-form-item label="启用 CDataPath">
        <el-switch v-model="configForm.contextMenu.enableCDataPath" />
      </el-form-item>

      <el-form-item label="启用 RowTag">
        <el-switch v-model="configForm.contextMenu.enableRowTag" />
      </el-form-item>

      <!-- 选中文本气泡配置 -->
      <el-form-item label="启用选中文本气泡">
        <el-switch v-model="configForm.selectionBubble.enabled" />
      </el-form-item>

      <el-form-item label="最小选中长度">
        <el-input-number
          v-model="configForm.selectionBubble.minSelectionLength"
          :min="1"
          :max="100"
          :step="1"
        />
      </el-form-item>

      <el-form-item label="最大选中长度">
        <el-input-number
          v-model="configForm.selectionBubble.maxSelectionLength"
          :min="100"
          :max="5000"
          :step="100"
        />
      </el-form-item>

      <el-form-item label="显示延迟(ms)">
        <el-input-number
          v-model="configForm.selectionBubble.showDelay"
          :min="0"
          :max="2000"
          :step="100"
        />
      </el-form-item>

      <el-form-item label="隐藏延迟(ms)">
        <el-input-number
          v-model="configForm.selectionBubble.hideDelay"
          :min="0"
          :max="1000"
          :step="50"
        />
      </el-form-item>

      <el-form-item label="自动隐藏">
        <el-switch v-model="configForm.selectionBubble.autoHide" />
      </el-form-item>

      <el-form-item label="自动隐藏时间(ms)">
        <el-input-number
          v-model="configForm.selectionBubble.hideTimeout"
          :min="1000"
          :max="10000"
          :step="500"
        />
      </el-form-item>

      <el-form-item label="只读模式启用">
        <el-switch v-model="configForm.selectionBubble.enableForReadOnly" />
      </el-form-item>

      <el-form-item label="启用复制功能">
        <el-switch v-model="configForm.selectionBubble.enableCopy" />
      </el-form-item>

      <el-form-item label="启用剪切功能">
        <el-switch v-model="configForm.selectionBubble.enableCut" />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <div class="footer-left">
          <el-button @click="handleReset" type="warning">重置配置</el-button>
          <!-- <el-button @click="handleInsertExample" type="info">插入示例代码</el-button> -->
        </div>
        <div class="footer-right">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确认</el-button>
        </div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, watch, computed } from 'vue';
  import { supportedLanguages, defaultEditorConfig } from './config.js';
  import { ElMessage } from 'element-plus';

  const props = defineProps({
    visible: { type: Boolean, default: false },
    config: { type: Object, default: () => ({}) },
  });

  const emit = defineEmits(['update:visible', 'update:config', 'insertExample']);

  // 创建一个深拷贝的配置表单，确保嵌套对象存在
  const initConfigForm = (config) => {
    const form = JSON.parse(JSON.stringify(config));
    // 确保ui对象存在
    if (!form.ui) {
      form.ui = { height: 500 };
    }
    // 确保minimap对象存在
    if (!form.minimap) {
      form.minimap = { enabled: true };
    }
    // 确保contextMenu对象存在
    if (!form.contextMenu) {
      form.contextMenu = {
        enableCustomMenu: true,
        hideDefaultMenu: true,
        enableFormatter: true,
        enableCDataPath: true,
        enableRowTag: true,
      };
    }
    // 确保selectionBubble对象存在
    if (!form.selectionBubble) {
      form.selectionBubble = {
        enabled: true,
        minSelectionLength: 1,
        maxSelectionLength: 1000,
        showDelay: 300,
        hideDelay: 100,
        autoHide: true,
        hideTimeout: 3000,
        enableForReadOnly: false,
        enableCopy: true,
        enableCut: true,
      };
    }
    return form;
  };

  const configForm = ref(initConfigForm(props.config));

  // 监听visible变化，当对话框打开时重置表单
  watch(
    () => props.visible,
    (val) => {
      if (val) {
        configForm.value = initConfigForm(props.config);
      }
    },
  );

  // 监听config变化，更新表单
  watch(
    () => props.config,
    (val) => {
      configForm.value = initConfigForm(val);
    },
    { deep: true },
  );

  // 关闭对话框
  const handleClose = () => {
    emit('update:visible', false);
  };

  // 确认修改
  const handleConfirm = () => {
    emit('update:config', configForm.value);
    handleClose();
  };

  // 重置配置
  const handleReset = () => {
    configForm.value = initConfigForm(defaultEditorConfig);
    ElMessage.success('配置已重置为默认值');
  };

  // 插入示例代码
  const handleInsertExample = () => {
    const exampleCode = `<?xml version="1.0" encoding="UTF-8"?>
<root>
  <data>
    <item id="1">
      <name>示例数据</name>
      <value>这是一个示例</value>
    </item>
  </data>
</root>`;

    // 通过emit发送示例代码到父组件
    emit('insertExample', exampleCode);
    ElMessage.success('示例代码已插入');
    handleClose();
  };

  // 计算属性：对话框可见性
  const dialogVisible = computed({
    get: () => props.visible,
    set: (val) => emit('update:visible', val),
  });
</script>

<style scoped>
  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .footer-left {
    display: flex;
    gap: 10px;
  }

  .footer-right {
    display: flex;
    gap: 10px;
  }
</style>
