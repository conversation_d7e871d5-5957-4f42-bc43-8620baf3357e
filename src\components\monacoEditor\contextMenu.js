/**
 * Monaco Editor 右键菜单相关功能
 * 包含自定义菜单项的添加和默认菜单项的隐藏功能
 */

import { setPrettierAction } from './formatter.js';

/**
 * 设置自定义右键菜单（添加自定义菜单项）
 * @param {Object} editor - Monaco编辑器实例
 * @param {Function} emit - Vue组件的emit函数
 * @param {Object} options - 配置选项
 */
export const setupCustomContextMenu = (editor, emit, options = {}) => {
  if (!editor) return;

  const config = {
    enableCDataPath: true,
    enableRowTag: true,
    enableFormatter: true,
    ...options,
  };

  addCustomMenuActions(editor, emit, config);
};
/**
 *  格式化完成后的回调函数
 * @param {*} newValue
 * @param {*} currentValue
 * @param {*} editor // 还是editor本身 经过反复的传递而已
 */
const handleFormatComplete = (newValue, currentValue, editor) => {
  console.log('处理格式化完成，检查结果', newValue);
  toRaw(editor).setValue(newValue);
  // 触发内容变化事件
  setTimeout(() => {
    emit('contentChange', newValue);
    emit('update:modelValue', newValue);
    emit('formatSuccess', {
      language: currentLanguage,
      originalLength: currentValue.length,
      newLength: newValue.length,
    });
  }, 0);
  console.log('代码格式化完成');
};
/**
 * 添加自定义菜单项
 * @param {Object} editor - Monaco编辑器实例
 * @param {Function} emit - Vue组件的emit函数
 * @param {Object} config - 配置选项
 */
const addCustomMenuActions = (editor, emit, config = {}) => {
  let menuOrder = 1;

  // 根据配置添加格式化菜单项
  if (config.enableFormatter) {
    setPrettierAction(editor, handleFormatComplete);
  }

  // 根据配置添加 CDataPath 菜单项
  if (config.enableCDataPath) {
    editor.addAction({
      id: 'write-to-cdata-path',
      label: '写到 CDataPath',
      keybindings: [],
      contextMenuGroupId: 'custom',
      contextMenuOrder: menuOrder++,
      run: (ed) => {
        try {
          const selection = ed.getSelection();
          const selectedText = selection ? ed.getModel().getValueInRange(selection) : '';

          setTimeout(() => {
            emit('writeToField', {
              type: 'CDataPath',
              selectedText,
              position: ed.getPosition(),
              selection,
            });
          }, 0);
        } catch (error) {
          console.error('写到 CDataPath 时出错:', error);
        }
      },
    });
  }

  // 根据配置添加 RowTag 菜单项
  if (config.enableRowTag) {
    editor.addAction({
      id: 'write-to-row-tag',
      label: '写到 RowTag',
      keybindings: [],
      contextMenuGroupId: 'custom',
      contextMenuOrder: menuOrder++,
      run: (ed) => {
        try {
          const selection = ed.getSelection();
          const selectedText = selection ? ed.getModel().getValueInRange(selection) : '';

          setTimeout(() => {
            emit('writeToField', {
              type: 'RowTag',
              selectedText,
              position: ed.getPosition(),
              selection,
            });
          }, 0);
        } catch (error) {
          console.error('写到 RowTag 时出错:', error);
        }
      },
    });
  }
};

/**
 * 隐藏默认右键菜单中的特定项
 * @param {*} editor - Monaco编辑器实例
 * @param {Object} options - 配置选项
 */
export const hideDefaultContextMenu = (editor, options = {}) => {
  if (!editor) {
    console.warn('Editor instance is required');
    return;
  }

  const config = {
    hideAllDefault: true, // 是否隐藏所有默认菜单项
    customLabels: ['写到 CDataPath', '写到 RowTag', '格式化代码'], // 保留的自定义菜单项
    ...options,
  };

  editor.onContextMenu(() => {
    console.log('Monaco context menu triggered');
    // 延迟执行，等待菜单DOM渲染完成
    setTimeout(() => {
      hideSpecificMenuItems(config);
    }, 50);
  });

  const editorContainer = editor.getDomNode();
  if (editorContainer) {
    editorContainer.addEventListener('contextmenu', () => {
      console.log('Editor container contextmenu triggered');

      setTimeout(() => {
        hideSpecificMenuItems(config);
      }, 50);
    });
  }
};

/**
 * 通过DOM操作隐藏特定菜单项
 * @param {Object} config - 配置选项
 */
function hideSpecificMenuItems(config) {
  const containerSelector = '#codeBox';
  const container = document.querySelector(containerSelector);
  if (!container) {
    console.log('Container not found.');
    return;
  }

  // 检查是否已有host
  let host = container.querySelector('.shadow-root-host');
  if (host) {
    setupHost(host, config);
    return; // 如果已存在，直接设置并返回
  }

  // 如果未找到，设置观察器等待host出现
  const containerObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE && node.matches('.shadow-root-host')) {
            console.log('Shadow root host added dynamically.');
            setupHost(node, config);
            containerObserver.disconnect(); // 找到后停止观察
          } else if (node.querySelector) {
            // 如果添加的节点有子节点，检查内部
            const innerHost = node.querySelector('.shadow-root-host');
            if (innerHost) {
              console.log('Shadow root host added inside subtree.');
              setupHost(innerHost, config);
              containerObserver.disconnect();
            }
          }
        });
      }
    });
  });

  containerObserver.observe(container, { childList: true, subtree: true });
}

/**
 * 设置Shadow DOM Host
 * @param {Element} host - Shadow DOM Host元素
 * @param {Object} config - 配置选项
 */
function setupHost(host, config) {
  const shadowRoot = host.shadowRoot;
  if (!shadowRoot) {
    console.log('No shadow root attached.');
    return;
  }

  // 设置MutationObserver监控shadowRoot中的动态添加
  const menuObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const menuContainer = node.matches('.monaco-menu-container')
              ? node
              : node.querySelector('.monaco-menu-container');
            if (menuContainer) {
              hideNonCustomItems(menuContainer, config);
            }
          }
        });
      }
    });
  });

  menuObserver.observe(shadowRoot, { childList: true, subtree: true });

  // 检查是否已有菜单存在
  const existingMenu = shadowRoot.querySelector('.monaco-menu-container');
  if (existingMenu) {
    hideNonCustomItems(existingMenu, config);
  }
}

/**
 * 隐藏非自定义菜单项
 * @param {Element} container - 菜单容器元素
 * @param {Object} config - 配置选项
 */
function hideNonCustomItems(container, config) {
  const customLabels = config?.customLabels || ['写到 CDataPath', '写到 RowTag', '格式化代码'];
  const hideAllDefault = config?.hideAllDefault !== false;

  let hiddenCount = 0;

  container.querySelectorAll('.action-item').forEach((menuItem) => {
    const labelElement = menuItem.querySelector('.action-label');
    if (labelElement) {
      const labelText = labelElement.textContent.trim();
      const ariaLabel = labelElement.getAttribute('aria-label') || '';

      // 检查是否是自定义项
      if (customLabels.includes(labelText) || customLabels.includes(ariaLabel)) {
        // 保留自定义项
        menuItem.style.display = '';
      } else if (hideAllDefault) {
        // 根据配置隐藏非自定义项
        menuItem.style.display = 'none';
        hiddenCount++;
      } else {
        // 如果不隐藏默认项，则显示
        menuItem.style.display = '';
      }
    } else if (hideAllDefault) {
      // 如果没有label且配置为隐藏默认项，也隐藏
      menuItem.style.display = 'none';
      hiddenCount++;
    }
  });

  // 调试信息
  console.log(`Hidden ${hiddenCount} non-custom menu items.`);
  if (hiddenCount === 0) {
    console.log('No non-custom menu items found. Available menu items:');
    container.querySelectorAll('.action-item .action-label').forEach((item) => {
      console.log(
        `- ${item.textContent.trim()} (aria-label: ${item.getAttribute('aria-label') || 'none'})`,
      );
    });
  }
}
