/**
 * Monaco Editor 代码补全功能
 * 包含各种编程语言的智能提示和代码片段
 */
import * as monaco from 'monaco-editor';

/**
 * Java语言代码补全配置
 */
export function setupJavaCodeCompletion() {
  monaco.languages.registerCompletionItemProvider('java', {
    triggerCharacters: ['.', '('],
    provideCompletionItems: async (model, position) => {
      const word = model.getWordUntilPosition(position);
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: word.startColumn,
        endColumn: word.endColumn,
      };

      return { suggestions: getJavaCompletionItems(range) };
    },
  });
}

/**
 * 生成Java代码补全建议
 * @param {Object} range - 文本范围
 * @returns {Array} 补全建议列表
 */
function getJavaCompletionItems(range) {
  const keywords = [
    'public',
    'private',
    'protected',
    'static',
    'void',
    'int',
    'String',
    'boolean',
    'char',
    'double',
    'float',
    'long',
    'short',
    'byte',
    'if',
    'else',
    'for',
    'while',
    'do',
    'switch',
    'case',
    'default',
    'break',
    'continue',
    'return',
    'class',
    'interface',
    'enum',
    'extends',
    'implements',
    'import',
    'package',
    'try',
    'catch',
    'finally',
    'throw',
    'throws',
    'new',
    'this',
    'super',
    'null',
    'true',
    'false',
  ];

  const commonClasses = [
    'ArrayList',
    'HashMap',
    'LinkedList',
    'HashSet',
    'TreeSet',
    'System',
    'String',
    'Integer',
    'Boolean',
    'Double',
    'Float',
    'Long',
    'Short',
    'Byte',
    'Character',
    'List',
    'Map',
    'Set',
    'Collections',
    'Arrays',
    'Math',
    'Object',
    'Exception',
    'RuntimeException',
    'Thread',
    'Runnable',
  ];

  const javaSnippets = [
    {
      label: 'System.out.println',
      kind: monaco.languages.CompletionItemKind.Method,
      insertText: 'System.out.println(${1:text});',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      documentation: 'Prints a string to the console',
      detail: 'System.out.println(Object x)',
    },
    {
      label: 'public static void main',
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: 'public static void main(String[] args) {\n\t${1:// TODO: Add your code here}\n}',
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      documentation: 'Main method entry point',
      detail: 'public static void main(String[] args)',
    },
    {
      label: 'if-else',
      kind: monaco.languages.CompletionItemKind.Snippet,
      insertText: [
        'if (${1:condition}) {',
        '\t${2:// TODO: Add your code here}',
        '} else {',
        '\t${3:// TODO: Add your code here}',
        '}',
      ].join('\n'),
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      documentation: 'Conditional statement',
      detail: 'if-else statement',
    },
    {
      label: 'for-i',
      kind: monaco.languages.CompletionItemKind.Snippet,
      insertText: [
        'for (int ${1:i} = 0; ${1:i} < ${2:length}; ${1:i}++) {',
        '\t${3:// TODO: Add your code here}',
        '}',
      ].join('\n'),
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      documentation: 'For loop with index',
      detail: 'for (int i = 0; i < length; i++)',
    },
    {
      label: 'for-each',
      kind: monaco.languages.CompletionItemKind.Snippet,
      insertText: [
        'for (${1:Type} ${2:item} : ${3:collection}) {',
        '\t${4:// TODO: Add your code here}',
        '}',
      ].join('\n'),
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      documentation: 'Enhanced for loop (for-each)',
      detail: 'for (Type item : collection)',
    },
    {
      label: 'while',
      kind: monaco.languages.CompletionItemKind.Snippet,
      insertText: ['while (${1:condition}) {', '\t${2:// TODO: Add your code here}', '}'].join(
        '\n',
      ),
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      documentation: 'While loop',
      detail: 'while (condition)',
    },
    {
      label: 'try-catch',
      kind: monaco.languages.CompletionItemKind.Snippet,
      insertText: [
        'try {',
        '\t${1:// TODO: Add your code here}',
        '} catch (${2:Exception} ${3:e}) {',
        '\t${4:// TODO: Handle exception}',
        '}',
      ].join('\n'),
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      documentation: 'Try-catch block',
      detail: 'try-catch exception handling',
    },
  ];

  return [
    ...keywords.map((keyword) => ({
      label: keyword,
      kind: monaco.languages.CompletionItemKind.Keyword,
      insertText: keyword,
      range,
      detail: `Java keyword: ${keyword}`,
    })),
    ...commonClasses.map((cls) => ({
      label: cls,
      kind: monaco.languages.CompletionItemKind.Class,
      insertText: cls,
      range,
      detail: `Java class: ${cls}`,
    })),
    ...javaSnippets.map((snippet) => ({ ...snippet, range })),
  ];
}

/**
 * SQL语言代码补全配置
 */
export function setupSQLCodeCompletion() {
  monaco.languages.registerCompletionItemProvider('sql', {
    triggerCharacters: [' ', '.', '('],
    provideCompletionItems: async (model, position) => {
      const word = model.getWordUntilPosition(position);
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: word.startColumn,
        endColumn: word.endColumn,
      };

      return { suggestions: getSQLCompletionItems(range) };
    },
  });
}

/**
 * 生成SQL代码补全建议
 * @param {Object} range - 文本范围
 * @returns {Array} 补全建议列表
 */
function getSQLCompletionItems(range) {
  const sqlKeywords = [
    'SELECT',
    'FROM',
    'WHERE',
    'INSERT',
    'UPDATE',
    'DELETE',
    'CREATE',
    'DROP',
    'ALTER',
    'TABLE',
    'INDEX',
    'VIEW',
    'DATABASE',
    'SCHEMA',
    'JOIN',
    'INNER JOIN',
    'LEFT JOIN',
    'RIGHT JOIN',
    'FULL JOIN',
    'ON',
    'GROUP BY',
    'ORDER BY',
    'HAVING',
    'LIMIT',
    'OFFSET',
    'UNION',
    'UNION ALL',
    'DISTINCT',
    'COUNT',
    'SUM',
    'AVG',
    'MAX',
    'MIN',
    'AND',
    'OR',
    'NOT',
    'IN',
    'EXISTS',
    'BETWEEN',
    'LIKE',
    'IS NULL',
    'IS NOT NULL',
    'AS',
    'ASC',
    'DESC',
  ];

  const sqlFunctions = [
    'COUNT()',
    'SUM()',
    'AVG()',
    'MAX()',
    'MIN()',
    'UPPER()',
    'LOWER()',
    'LENGTH()',
    'SUBSTRING()',
    'CONCAT()',
    'NOW()',
    'CURDATE()',
    'CURTIME()',
    'DATE_FORMAT()',
    'YEAR()',
    'MONTH()',
    'DAY()',
  ];

  const sqlSnippets = [
    {
      label: 'SELECT statement',
      kind: monaco.languages.CompletionItemKind.Snippet,
      insertText: ['SELECT ${1:columns}', 'FROM ${2:table_name}', 'WHERE ${3:condition};'].join(
        '\n',
      ),
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      documentation: 'Basic SELECT statement',
      detail: 'SELECT columns FROM table WHERE condition',
    },
    {
      label: 'INSERT statement',
      kind: monaco.languages.CompletionItemKind.Snippet,
      insertText: ['INSERT INTO ${1:table_name} (${2:columns})', 'VALUES (${3:values});'].join(
        '\n',
      ),
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      documentation: 'INSERT statement',
      detail: 'INSERT INTO table (columns) VALUES (values)',
    },
    {
      label: 'UPDATE statement',
      kind: monaco.languages.CompletionItemKind.Snippet,
      insertText: [
        'UPDATE ${1:table_name}',
        'SET ${2:column} = ${3:value}',
        'WHERE ${4:condition};',
      ].join('\n'),
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      documentation: 'UPDATE statement',
      detail: 'UPDATE table SET column = value WHERE condition',
    },
    {
      label: 'DELETE statement',
      kind: monaco.languages.CompletionItemKind.Snippet,
      insertText: ['DELETE FROM ${1:table_name}', 'WHERE ${2:condition};'].join('\n'),
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      documentation: 'DELETE statement',
      detail: 'DELETE FROM table WHERE condition',
    },
  ];

  return [
    ...sqlKeywords.map((keyword) => ({
      label: keyword,
      kind: monaco.languages.CompletionItemKind.Keyword,
      insertText: keyword,
      range,
      detail: `SQL keyword: ${keyword}`,
    })),
    ...sqlFunctions.map((func) => ({
      label: func,
      kind: monaco.languages.CompletionItemKind.Function,
      insertText: func,
      insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
      range,
      detail: `SQL function: ${func}`,
    })),
    ...sqlSnippets.map((snippet) => ({ ...snippet, range })),
  ];
}
