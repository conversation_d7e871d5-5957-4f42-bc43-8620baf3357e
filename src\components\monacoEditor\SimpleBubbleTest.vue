<template>
  <div class="simple-bubble-test">
    <el-card>
      <template #header>
        <h3>Monaco编辑器选中文本气泡测试 - 简化版</h3>
      </template>

      <div class="test-info">
        <el-alert
          title="功能说明"
          type="info"
          :closable="false"
          show-icon
        >
          <p><strong>已保留功能：</strong></p>
          <ul>
            <li>✅ 复制 - 将选中文本复制到剪贴板</li>
            <li>✅ 剪切 - 将选中文本剪切到剪贴板并删除原文</li>
          </ul>
          <p><strong>已移除功能：</strong></p>
          <ul>
            <li>❌ 搜索 - 已移除</li>
            <li>❌ 翻译 - 已移除</li>
            <li>❌ 替换 - 已移除</li>
          </ul>
        </el-alert>
      </div>

      <div class="editor-section">
        <h4>测试编辑器</h4>
        <div class="editor-wrapper">
          <MonacoEditor
            ref="editorRef"
            v-model="testContent"
            @bubbleAction="handleBubbleAction"
            @contentChange="handleContentChange"
          />
        </div>
      </div>

      <div class="status-section">
        <h4>操作状态</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="最后操作">
            <el-tag v-if="lastAction" :type="getActionType(lastAction)">
              {{ lastAction }}
            </el-tag>
            <span v-else>无</span>
          </el-descriptions-item>
          <el-descriptions-item label="操作时间">
            {{ lastActionTime || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="选中文本">
            <el-text class="selected-text">{{ lastSelectedText || '无' }}</el-text>
          </el-descriptions-item>
          <el-descriptions-item label="内容长度">
            {{ testContent.length }} 字符
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="log-section">
        <h4>操作日志</h4>
        <div class="log-container">
          <div v-if="logs.length === 0" class="no-logs">
            暂无操作记录，请在编辑器中选中文本进行测试
          </div>
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <el-tag :type="getLogType(log.type)" size="small">{{ log.type }}</el-tag>
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <div class="log-actions">
          <el-button @click="clearLogs" size="small" type="warning">清空日志</el-button>
          <el-button @click="testClipboard" size="small" type="info">测试剪贴板</el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import MonacoEditor from './index.vue';

  // 测试内容
  const testContent = ref(`这是一个测试文档，用于验证选中文本气泡功能。

请选中下面的任意文本来测试复制和剪切功能：

1. 这是第一行测试文本
2. 这是第二行测试文本  
3. 这是第三行测试文本

JavaScript代码示例：
function testFunction() {
  console.log('Hello World');
  return 'Test';
}

XML代码示例：
<root>
  <item>测试数据</item>
</root>

选中任意文本，应该会弹出包含"复制"和"剪切"按钮的气泡。`);

  // 编辑器引用
  const editorRef = ref(null);

  // 状态数据
  const lastAction = ref('');
  const lastSelectedText = ref('');
  const lastActionTime = ref('');

  // 日志数据
  const logs = ref([]);

  // 添加日志
  const addLog = (type, message) => {
    const now = new Date();
    logs.value.unshift({
      time: now.toLocaleTimeString(),
      type,
      message,
    });
    
    // 限制日志数量
    if (logs.value.length > 50) {
      logs.value = logs.value.slice(0, 50);
    }
  };

  // 处理气泡操作
  const handleBubbleAction = (actionData) => {
    const { action, selectedText } = actionData;
    
    lastAction.value = action.label;
    lastSelectedText.value = selectedText;
    lastActionTime.value = new Date().toLocaleTimeString();
    
    addLog('ACTION', `执行${action.label}操作: "${selectedText.substring(0, 30)}${selectedText.length > 30 ? '...' : ''}"`);
    
    console.log('🎯 [Test] 气泡操作:', actionData);
  };

  // 处理内容变化
  const handleContentChange = (content) => {
    addLog('CHANGE', `内容已更改，当前长度: ${content.length} 字符`);
  };

  // 获取操作类型样式
  const getActionType = (action) => {
    switch (action) {
      case '复制': return 'primary';
      case '剪切': return 'warning';
      default: return 'info';
    }
  };

  // 获取日志类型样式
  const getLogType = (type) => {
    switch (type) {
      case 'ACTION': return 'success';
      case 'CHANGE': return 'warning';
      case 'ERROR': return 'danger';
      default: return 'info';
    }
  };

  // 清空日志
  const clearLogs = () => {
    logs.value = [];
    addLog('INFO', '日志已清空');
  };

  // 测试剪贴板
  const testClipboard = async () => {
    try {
      if (navigator.clipboard) {
        const text = await navigator.clipboard.readText();
        addLog('INFO', `剪贴板内容: "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);
      } else {
        addLog('WARN', '浏览器不支持剪贴板API');
      }
    } catch (err) {
      addLog('ERROR', `读取剪贴板失败: ${err.message}`);
    }
  };

  // 初始化日志
  addLog('INFO', '选中文本气泡测试页面已加载');
  addLog('INFO', '请在编辑器中选中文本测试复制和剪切功能');
</script>

<style scoped>
  .simple-bubble-test {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .test-info {
    margin-bottom: 20px;
  }

  .test-info ul {
    margin: 10px 0;
    padding-left: 20px;
  }

  .test-info li {
    margin: 5px 0;
  }

  .editor-section {
    margin-bottom: 20px;
  }

  .editor-section h4,
  .status-section h4,
  .log-section h4 {
    margin-bottom: 10px;
    color: #409eff;
  }

  .editor-wrapper {
    height: 300px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
  }

  .status-section {
    margin-bottom: 20px;
  }

  .selected-text {
    max-width: 200px;
    word-break: break-all;
  }

  .log-section {
    margin-bottom: 20px;
  }

  .log-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
    background-color: #fafafa;
    margin-bottom: 10px;
  }

  .no-logs {
    text-align: center;
    color: #909399;
    padding: 20px;
  }

  .log-item {
    display: flex;
    align-items: center;
    padding: 4px 0;
    border-bottom: 1px solid #e4e7ed;
    gap: 10px;
  }

  .log-item:last-child {
    border-bottom: none;
  }

  .log-time {
    color: #909399;
    font-size: 12px;
    min-width: 80px;
  }

  .log-message {
    color: #606266;
    font-size: 13px;
    flex: 1;
    word-break: break-all;
  }

  .log-actions {
    display: flex;
    gap: 10px;
  }
</style>
