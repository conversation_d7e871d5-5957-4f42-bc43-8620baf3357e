/**
 * Monaco Editor 选中文本气泡功能
 * 处理文本选择事件和气泡显示逻辑
 */

import { toRaw } from 'vue';

/**
 * 设置选中文本气泡功能
 * @param {Object} editor - Monaco编辑器实例
 * @param {Function} showBubble - 显示气泡的回调函数
 * @param {Function} hideBubble - 隐藏气泡的回调函数
 * @param {Object} options - 配置选项
 */
export const setupSelectionBubble = (editor, showBubble, hideBubble, options = {}) => {
  console.log('🚀 [SelectionBubble] 初始化选中文本气泡功能');

  if (!editor) {
    console.warn('❌ [SelectionBubble] Editor instance is required for selection bubble');
    return;
  }

  const config = {
    minSelectionLength: 1, // 最小选中文本长度
    maxSelectionLength: 1000, // 最大选中文本长度
    showDelay: 300, // 显示延迟（毫秒）
    hideDelay: 100, // 隐藏延迟（毫秒）
    enableForReadOnly: false, // 是否在只读模式下启用
    ...options,
  };

  console.log('⚙️ [SelectionBubble] 配置信息:', config);

  let showTimer = null;
  let hideTimer = null;
  let lastSelection = null;

  // 清除显示计时器
  const clearShowTimer = () => {
    if (showTimer) {
      clearTimeout(showTimer);
      showTimer = null;
    }
  };

  // 清除隐藏计时器
  const clearHideTimer = () => {
    if (hideTimer) {
      clearTimeout(hideTimer);
      hideTimer = null;
    }
  };

  // 获取选中文本的屏幕坐标
  const getSelectionScreenPosition = (selection) => {
    console.log('📍 [SelectionBubble] 开始计算选中文本位置:', selection);

    try {
      const editorDom = toRaw(editor).getDomNode();
      console.log('🏠 [SelectionBubble] 编辑器DOM:', editorDom);

      if (!editorDom) {
        console.log('❌ [SelectionBubble] 编辑器DOM不存在');
        return null;
      }

      // 获取选中区域的DOM范围
      const range = toRaw(editor).getModel().getValueInRange(selection);
      console.log('📝 [SelectionBubble] 选中文本:', range);

      if (!range) {
        console.log('❌ [SelectionBubble] 无法获取选中文本');
        return null;
      }

      // 计算选中文本的中心位置
      const startPosition = toRaw(editor).getScrolledVisiblePosition({
        lineNumber: selection.startLineNumber,
        column: selection.startColumn,
      });

      const endPosition = toRaw(editor).getScrolledVisiblePosition({
        lineNumber: selection.endLineNumber,
        column: selection.endColumn,
      });

      console.log('📐 [SelectionBubble] 位置信息:', {
        startPosition,
        endPosition,
      });

      if (!startPosition || !endPosition) {
        console.log('❌ [SelectionBubble] 无法获取位置信息');
        return null;
      }

      // 计算中心点
      const centerX = (startPosition.left + endPosition.left) / 2;
      const centerY = Math.min(startPosition.top, endPosition.top);

      // 获取编辑器容器的位置信息
      const editorRect = editorDom.getBoundingClientRect();

      console.log('📏 [SelectionBubble] 详细位置信息:', {
        选中区域: {
          startPosition,
          endPosition,
          centerX,
          centerY,
        },
        编辑器容器: {
          left: editorRect.left,
          top: editorRect.top,
          width: editorRect.width,
          height: editorRect.height,
        },
        视窗信息: {
          innerWidth: window.innerWidth,
          innerHeight: window.innerHeight,
          scrollX: window.scrollX,
          scrollY: window.scrollY,
        },
      });

      const result = {
        x: centerX,
        y: centerY,
        editorRect,
      };

      console.log('✅ [SelectionBubble] 位置计算完成:', result);
      return result;
    } catch (error) {
      console.error('❌ [SelectionBubble] 获取选中文本位置失败:', error);
      return null;
    }
  };

  // 检查是否应该显示气泡
  const shouldShowBubble = (selection, selectedText) => {
    console.log('🔍 [SelectionBubble] 检查显示条件:', {
      selectedText,
      textLength: selectedText?.length,
      config,
    });

    // 检查是否有选中文本
    if (!selectedText || selectedText.trim().length === 0) {
      console.log('❌ [SelectionBubble] 没有选中文本或文本为空');
      return false;
    }

    // 检查文本长度
    if (
      selectedText.length < config.minSelectionLength ||
      selectedText.length > config.maxSelectionLength
    ) {
      console.log('❌ [SelectionBubble] 文本长度不符合要求:', {
        length: selectedText.length,
        min: config.minSelectionLength,
        max: config.maxSelectionLength,
      });
      return false;
    }

    // 检查只读模式
    const isReadOnly = toRaw(editor).getOptions().get('readOnly');
    if (!config.enableForReadOnly && isReadOnly) {
      console.log('❌ [SelectionBubble] 只读模式下不启用气泡');
      return false;
    }

    // 检查选中区域是否有效
    if (
      !selection ||
      (selection.startLineNumber === selection.endLineNumber &&
        selection.startColumn === selection.endColumn)
    ) {
      console.log('❌ [SelectionBubble] 选中区域无效');
      return false;
    }

    console.log('✅ [SelectionBubble] 满足显示条件');
    return true;
  };

  // 处理选中文本变化
  const handleSelectionChange = () => {
    console.log('🔍 [SelectionBubble] 选中文本变化事件触发');
    clearShowTimer();
    clearHideTimer();

    const selection = toRaw(editor).getSelection();
    const selectedText = selection ? toRaw(editor).getModel().getValueInRange(selection) : '';

    console.log('📝 [SelectionBubble] 选中信息:', {
      selection,
      selectedText,
      textLength: selectedText.length,
    });

    // 如果没有选中文本或选中文本没有变化，隐藏气泡
    if (!shouldShowBubble(selection, selectedText)) {
      console.log('❌ [SelectionBubble] 不满足显示条件，隐藏气泡');
      hideTimer = setTimeout(() => {
        hideBubble();
        lastSelection = null;
      }, config.hideDelay);
      return;
    }

    // 检查选中文本是否发生变化
    const selectionKey = `${selection.startLineNumber}-${selection.startColumn}-${selection.endLineNumber}-${selection.endColumn}`;
    const lastSelectionKey = lastSelection
      ? `${lastSelection.startLineNumber}-${lastSelection.startColumn}-${lastSelection.endLineNumber}-${lastSelection.endColumn}`
      : '';

    if (selectionKey === lastSelectionKey) {
      return; // 选中文本没有变化，不需要重新显示
    }

    lastSelection = selection;

    // 延迟显示气泡
    console.log(`⏰ [SelectionBubble] 延迟 ${config.showDelay}ms 后显示气泡`);
    showTimer = setTimeout(() => {
      console.log('🎈 [SelectionBubble] 开始显示气泡');
      const position = getSelectionScreenPosition(selection);
      console.log('📍 [SelectionBubble] 计算位置:', position);

      if (position) {
        const bubbleData = {
          selectedText,
          selection,
          position,
          editorContainer: toRaw(editor).getDomNode(),
        };
        console.log('📦 [SelectionBubble] 气泡数据:', bubbleData);
        showBubble(bubbleData);
      } else {
        console.log('❌ [SelectionBubble] 无法计算气泡位置');
      }
    }, config.showDelay);
  };

  // 监听选中文本变化
  const selectionChangeDisposable = toRaw(editor).onDidChangeCursorSelection((e) => {
    handleSelectionChange();
  });

  // 监听编辑器失去焦点
  const blurDisposable = toRaw(editor).onDidBlurEditorText(() => {
    clearShowTimer();
    clearHideTimer();
    hideTimer = setTimeout(() => {
      hideBubble();
      lastSelection = null;
    }, config.hideDelay);
  });

  // 监听编辑器内容变化（可能影响选中文本）
  const contentChangeDisposable = toRaw(editor).onDidChangeModelContent(() => {
    // 内容变化时，重新检查选中文本
    setTimeout(handleSelectionChange, 50);
  });

  // 返回清理函数
  return () => {
    clearShowTimer();
    clearHideTimer();

    if (selectionChangeDisposable) {
      selectionChangeDisposable.dispose();
    }
    if (blurDisposable) {
      blurDisposable.dispose();
    }
    if (contentChangeDisposable) {
      contentChangeDisposable.dispose();
    }
  };
};

/**
 * 默认气泡操作配置
 */
export const defaultBubbleActions = [
  {
    id: 'copy',
    label: '复制',
    type: 'primary',
    icon: 'DocumentCopy',
  },
  {
    id: 'cut',
    label: '剪切',
    type: 'warning',
    icon: 'Scissors',
  },
];

/**
 * 处理气泡操作
 * @param {Object} actionData - 操作数据
 * @param {Object} editor - Monaco编辑器实例
 * @param {Function} emit - Vue组件的emit函数
 */
export const handleBubbleAction = (actionData, editor, emit) => {
  const { action, selectedText, selection } = actionData;

  console.log('🎯 [SelectionBubble] 处理操作:', action.id, selectedText);

  switch (action.id) {
    case 'copy':
      handleCopyAction(selectedText);
      break;
    case 'cut':
      handleCutAction(selectedText, selection, editor, emit);
      break;
    default:
      // 触发自定义操作事件
      emit('bubbleAction', actionData);
  }
};

// 复制操作
const handleCopyAction = async (text) => {
  try {
    if (!text || text.trim().length === 0) {
      console.warn('⚠️ [SelectionBubble] 复制失败: 没有选中文本');
      return;
    }

    // 优先使用现代的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      console.log(
        '✅ [SelectionBubble] 文本已复制到剪贴板:',
        text.substring(0, 50) + (text.length > 50 ? '...' : ''),
      );
    } else {
      // 降级到传统方法
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        const successful = document.execCommand('copy');
        if (successful) {
          console.log(
            '✅ [SelectionBubble] 文本已复制到剪贴板 (降级方法):',
            text.substring(0, 50) + (text.length > 50 ? '...' : ''),
          );
        } else {
          throw new Error('execCommand 复制失败');
        }
      } finally {
        document.body.removeChild(textArea);
      }
    }
  } catch (err) {
    console.error('❌ [SelectionBubble] 复制失败:', err);
    // 可以在这里添加用户提示
  }
};

// 剪切操作
const handleCutAction = async (text, selection, editor, emit) => {
  try {
    if (!text || text.trim().length === 0) {
      console.warn('⚠️ [SelectionBubble] 剪切失败: 没有选中文本');
      return;
    }

    if (!selection || !editor) {
      console.warn('⚠️ [SelectionBubble] 剪切失败: 缺少选中区域或编辑器实例');
      return;
    }

    // 检查编辑器是否为只读模式
    if (toRaw(editor).getOptions().get('readOnly')) {
      console.warn('⚠️ [SelectionBubble] 剪切失败: 编辑器处于只读模式');
      return;
    }

    // 先复制文本到剪贴板
    let copySuccess = false;
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      copySuccess = true;
    } else {
      // 降级到传统方法
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        copySuccess = document.execCommand('copy');
      } finally {
        document.body.removeChild(textArea);
      }
    }

    if (copySuccess) {
      // 删除选中文本
      toRaw(editor).executeEdits('cut-operation', [
        {
          range: selection,
          text: '',
        },
      ]);

      // 触发内容变化事件
      const newContent = toRaw(editor).getValue();
      emit('contentChange', newContent);
      emit('update:modelValue', newContent);

      console.log(
        '✅ [SelectionBubble] 文本已剪切:',
        text.substring(0, 50) + (text.length > 50 ? '...' : ''),
      );
    } else {
      throw new Error('复制到剪贴板失败');
    }
  } catch (err) {
    console.error('❌ [SelectionBubble] 剪切失败:', err);
    // 可以在这里添加用户提示
  }
};
