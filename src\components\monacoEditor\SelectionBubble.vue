<template>
  <div
    v-if="visible"
    class="selection-bubble"
    :style="bubbleStyle"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div class="bubble-content">
      <!-- 默认操作按钮 -->
      <div class="bubble-actions" v-if="!customContent">
        <el-button
          v-for="action in actions"
          :key="action.id"
          :type="action.type || 'default'"
          :size="action.size || 'small'"
          :icon="action.icon"
          @click="handleActionClick(action)"
          class="bubble-btn"
        >
          {{ action.label }}
        </el-button>
      </div>

      <!-- 自定义内容 -->
      <div class="bubble-custom" v-if="customContent">
        <slot name="custom" :selectedText="selectedText" :selection="selection">
          <div v-html="customContent"></div>
        </slot>
      </div>
    </div>

    <!-- 气泡箭头 -->
    <div class="bubble-arrow" :class="arrowPosition"></div>
  </div>
</template>

<script setup>
  import { ref, computed, watch, nextTick, onBeforeUnmount } from 'vue';
  import { ElButton } from 'element-plus';

  const props = defineProps({
    visible: { type: Boolean, default: false },
    selectedText: { type: String, default: '' },
    selection: { type: Object, default: null },
    position: { type: Object, default: null },
    editorContainer: { type: Object, default: null },
    customContent: { type: String, default: '' },
    actions: {
      type: Array,
      default: () => [
        {
          id: 'copy',
          label: '复制',
          type: 'primary',
          icon: 'DocumentCopy',
        },
        {
          id: 'cut',
          label: '剪切',
          type: 'warning',
          icon: 'Scissors',
        },
      ],
    },
    autoHide: { type: Boolean, default: true },
    hideDelay: { type: Number, default: 3000 },
  });

  const emit = defineEmits(['action-click', 'hide', 'show']);

  const bubbleRef = ref(null);
  const isHovering = ref(false);
  const hideTimer = ref(null);

  // 计算气泡位置
  const bubbleStyle = computed(() => {
    if (!props.position || !props.editorContainer) {
      return { display: 'none' };
    }

    try {
      const { x, y, editorRect } = props.position;

      // 计算相对于页面的绝对位置
      const pageX = editorRect.left + x;
      const pageY = editorRect.top + y;

      // 气泡显示在选中文本上方
      const bubbleX = pageX;
      const bubbleY = pageY - 50; // 在选中文本上方50px

      // 确保气泡不会超出视窗边界
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const bubbleWidth = 200; // 估算气泡宽度
      const bubbleHeight = 40; // 估算气泡高度

      let finalX = bubbleX;
      let finalY = bubbleY;

      // 水平边界检查
      if (finalX + bubbleWidth > viewportWidth) {
        finalX = viewportWidth - bubbleWidth - 10;
      }
      if (finalX < 10) {
        finalX = 10;
      }

      // 垂直边界检查
      if (finalY < 10) {
        finalY = pageY + 30; // 如果上方空间不够，显示在下方
      }
      if (finalY + bubbleHeight > viewportHeight) {
        finalY = viewportHeight - bubbleHeight - 10;
      }

      console.log('🎈 [SelectionBubble] 位置计算:', {
        原始位置: { x, y },
        编辑器位置: editorRect,
        页面位置: { pageX, pageY },
        最终位置: { finalX, finalY },
      });

      return {
        position: 'fixed', // 使用fixed定位相对于视窗
        left: `${finalX}px`,
        top: `${finalY}px`,
        zIndex: 1000,
      };
    } catch (error) {
      console.warn('计算气泡位置失败:', error);
      return { display: 'none' };
    }
  });

  // 箭头位置
  const arrowPosition = computed(() => {
    return 'bottom'; // 默认箭头在底部，指向选中文本
  });

  // 处理操作按钮点击
  const handleActionClick = (action) => {
    console.log('🎯 [SelectionBubble] 操作按钮点击:', action.id);

    emit('action-click', {
      action,
      selectedText: props.selectedText,
      selection: props.selection,
    });

    // 根据操作类型执行默认行为
    switch (action.id) {
      case 'copy':
        handleCopy();
        break;
      case 'cut':
        handleCut();
        break;
    }

    // 操作完成后隐藏气泡
    setTimeout(() => {
      console.log('✅ [SelectionBubble] 操作完成，隐藏气泡');
      emit('update:visible', false);
    }, 100);
  };

  // 复制文本
  const handleCopy = async () => {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(props.selectedText);
        console.log('✅ [SelectionBubble] 文本已复制到剪贴板');
      } else {
        // 降级方法
        const textArea = document.createElement('textarea');
        textArea.value = props.selectedText;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
          document.execCommand('copy');
          console.log('✅ [SelectionBubble] 文本已复制到剪贴板 (降级方法)');
        } finally {
          document.body.removeChild(textArea);
        }
      }
    } catch (err) {
      console.error('❌ [SelectionBubble] 复制失败:', err);
    }
  };

  // 剪切文本（仅复制，实际删除由父组件处理）
  const handleCut = async () => {
    try {
      // 先复制文本
      await handleCopy();
      console.log('✅ [SelectionBubble] 剪切操作：文本已复制，等待父组件删除原文');
    } catch (err) {
      console.error('❌ [SelectionBubble] 剪切失败:', err);
    }
  };

  // 鼠标进入气泡
  const handleMouseEnter = () => {
    isHovering.value = true;
    clearHideTimer();
  };

  // 鼠标离开气泡
  const handleMouseLeave = () => {
    isHovering.value = false;
    if (props.autoHide) {
      console.log('🖱️ [SelectionBubble] 鼠标离开，开始隐藏计时器');
      startHideTimer();
    }
  };

  // 开始隐藏计时器
  const startHideTimer = () => {
    clearHideTimer();
    hideTimer.value = setTimeout(() => {
      if (!isHovering.value) {
        console.log('⏰ [SelectionBubble] 自动隐藏气泡');
        emit('update:visible', false);
      }
    }, props.hideDelay);
  };

  // 清除隐藏计时器
  const clearHideTimer = () => {
    if (hideTimer.value) {
      clearTimeout(hideTimer.value);
      hideTimer.value = null;
    }
  };

  // 监听visible变化
  watch(
    () => props.visible,
    (newVal, oldVal) => {
      // 避免重复处理相同的值
      if (newVal === oldVal) return;

      console.log('👁️ [SelectionBubble] 可见性变化:', newVal);
      if (newVal) {
        console.log('📍 [SelectionBubble] 显示气泡，位置数据:', props.position);
        emit('show');
        if (props.autoHide && !isHovering.value) {
          startHideTimer();
        }
      } else {
        clearHideTimer();
      }
    },
    { flush: 'post' }, // 在DOM更新后执行
  );

  // 组件销毁时清理计时器
  onBeforeUnmount(() => {
    clearHideTimer();
  });
</script>

<style scoped>
  .selection-bubble {
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 8px 12px;
    max-width: 300px;
    font-size: 14px;
    transition: all 0.2s ease;
    white-space: nowrap;
    pointer-events: auto;
  }

  .selection-bubble:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  .bubble-content {
    position: relative;
  }

  .bubble-actions {
    display: flex;
    gap: 6px;
    align-items: center;
  }

  .bubble-btn {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
  }

  .bubble-custom {
    max-height: 200px;
    overflow-y: auto;
  }

  .bubble-arrow {
    position: absolute;
    width: 0;
    height: 0;
  }

  .bubble-arrow.bottom {
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #ffffff;
  }

  .bubble-arrow.bottom::before {
    content: '';
    position: absolute;
    bottom: 1px;
    left: -7px;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-top: 7px solid #e4e7ed;
  }

  /* 暗色主题适配 */
  .dark .selection-bubble {
    background: #2d2d2d;
    border-color: #4c4d4f;
    color: #ffffff;
  }

  .dark .bubble-arrow.bottom {
    border-top-color: #2d2d2d;
  }

  .dark .bubble-arrow.bottom::before {
    border-top-color: #4c4d4f;
  }
</style>
