// 支持的语言列表
export const supportedLanguages = [
  { label: 'JavaScript', value: 'javascript' },
  { label: 'TypeScript', value: 'typescript' },
  { label: 'HTML', value: 'html' },
  { label: 'CSS', value: 'css' },
  { label: 'JSON', value: 'json' },
  { label: 'Java', value: 'java' },
  { label: 'SQL', value: 'sql' },
  { label: 'Groovy', value: 'groovy' },
  { label: 'Shell', value: 'shell' },
  { label: 'Python', value: 'python' },
  { label: 'XML', value: 'xml' },
  { label: '自定义语言', value: 'MyLanguage' },
];

// 默认编辑器配置
export const defaultEditorConfig = {
  theme: 'vs-dark', // 编辑器主题：vs, vs-dark, hc-black
  autoIndex: true, // 自动索引
  language: 'xml', // 默认语言
  tabCompletion: 'on', // Tab键自动完成
  cursorSmoothCaretAnimation: true, // 光标平滑动画
  formatOnPaste: true, // 粘贴时自动格式化
  mouseWheelZoom: true, // 鼠标滚轮缩放
  folding: true, // 代码折叠
  autoClosingBrackets: 'always', // 自动闭合括号
  autoClosingOvertype: 'always', // 自动覆盖闭合字符
  autoClosingQuotes: 'always', // 自动闭合引号
  automaticLayout: true, // 自动布局
  readOnly: false, // 只读模式
  minimap: {
    // 小地图配置
    enabled: true, // 启用小地图
    maxColumn: 120, // 最大列数
    renderCharacters: true, // 渲染字符
    scale: 1, // 缩放比例
    showSlider: 'mouseover', // 显示滑块：always, mouseover
    side: 'right', // 位置：right, left
  },
  lineNumbers: 'on', // 行号显示
  lineDecorationsWidth: 10, // 行装饰宽度
  lineNumbersMinChars: 5, // 行号最小字符数
  glyphMargin: true, // 图标边距
  scrollBeyondLastLine: true, // 滚动超过最后一行
  scrollbar: {
    // 滚动条配置
    useShadows: true, // 使用阴影
    verticalScrollbarSize: 10, // 垂直滚动条大小
    horizontalScrollbarSize: 10, // 水平滚动条大小
  },
  wordWrap: 'off', // 自动换行：off, on, wordWrapColumn, bounded
  wordWrapColumn: 80, // 自动换行列数
  fontSize: 14, // 字体大小
  fontFamily: 'Consolas, "Courier New", monospace', // 字体

  // 编辑器UI配置
  ui: {
    height: 500, // 编辑器高度
    needTips: false, // 是否显示注释按钮
    needTime: false, // 是否显示时间插入按钮
    disabled: false, // 是否禁用编辑器
    showMinimap: false, // 是否显示小地图
  },

  // 右键菜单配置
  contextMenu: {
    enableCustomMenu: true, // 启用自定义右键菜单
    hideDefaultMenu: true, // 隐藏默认右键菜单项
    hideAllDefault: true, // 是否隐藏所有默认菜单项
    enableCDataPath: true, // 启用 CDataPath 菜单项
    enableRowTag: true, // 启用 RowTag 菜单项
    enableFormatter: true, // 启用格式化菜单项
    customLabels: ['写到 CDataPath', '写到 RowTag', '格式化代码'], // 保留的自定义菜单项标签
  },

  // 选中文本气泡配置
  selectionBubble: {
    enabled: true, // 启用选中文本气泡
    minSelectionLength: 1, // 最小选中文本长度
    maxSelectionLength: 1000, // 最大选中文本长度
    showDelay: 300, // 显示延迟（毫秒）
    hideDelay: 100, // 隐藏延迟（毫秒）
    autoHide: true, // 自动隐藏
    hideTimeout: 3000, // 自动隐藏时间（毫秒）
    enableForReadOnly: false, // 是否在只读模式下启用
    enableCopy: true, // 启用复制功能
    enableCut: true, // 启用剪切功能
  },
};

// 语言配置
export const languageConfig = {
  brackets: [
    ['{', '}'],
    ['[', ']'],
    ['(', ')'],
  ],
  autoClosingPairs: [
    { open: '{', close: '}' },
    { open: '[', close: ']' },
    { open: '(', close: ')' },
    { open: '"', close: '', notIn: ['string'] },
    { open: "'", close: '', notIn: ['string', 'comment'] },
    { open: '`', close: '`', notIn: ['string', 'comment'] },
  ],
};

// 语法高亮配置
export const syntaxHighlightConfig = {
  ignoreCase: true,
  tokenizer: {
    root: [
      [/pageData|currentUser/, { token: 'keyword' }],
      [/[+]|[-]|[*]|[\/]|[%]|[>]|[<]|[=]|[!]|[:]|[&&]|[||]/, { token: 'string' }],
      [/'.*?'|".*?"/, { token: 'string.escape' }],
      [/#--.*?--#/, { token: 'comment' }],
      [/null/, { token: 'regexp' }],
      [/[{]|[}]/, { token: 'type' }],
      [/(?!.*[a-zA-Z])[0-9]/, { token: 'number.hex' }],
      [/[(]|[)]/, { token: 'number.octal' }],
    ],
  },
};

// 悬浮提示数据
export const hoverProviderData = [
  {
    text: 'SqlUtil',
    title: '数据库操作',
    content: '对数据进行操作，支持sql,addConditionExist等操作',
  },
];

// 注意：编辑器配置对话框组件已移至 EditorConfigDialog.vue

// 悬浮提示处理函数
export const hoverTips = (arr, word) => {
  let tip = '';
  arr.forEach((item) => {
    if (word === item.text) {
      tip = {
        contents: [{ value: item.title || '' }, { value: item.content || '' }],
      };
    }
  });
  return tip;
};

// 注册语言和提供者函数
export const registerLanguageProviders = (monaco, provider, myLang, color, hover, vCompletion) => {
  // 注册自定义语言
  monaco.languages.register({ id: 'MyLanguage' });

  // 为自定义语言注册代码补全提供者
  provider.value = monaco.languages.registerCompletionItemProvider('MyLanguage', {
    provideCompletionItems() {
      return {
        suggestions: JSON.parse(JSON.stringify(vCompletion)),
      };
    },
    triggerCharacters: ['.', '#', '$'],
  });

  // 自定义语言配置
  myLang.value = monaco.languages.setLanguageConfiguration('MyLanguage', languageConfig);

  // 自定义语法高亮配置
  color.value = monaco.languages.setMonarchTokensProvider('MyLanguage', syntaxHighlightConfig);

  // 自定义悬浮提示配置
  hover.value = monaco.languages.registerHoverProvider('MyLanguage', {
    provideHover: (model, position) => {
      if (model.getWordAtPosition(position) != null) {
        const word = model.getWordAtPosition(position).word;
        return hoverTips(hoverProviderData, word);
      }
    },
  });

  // 为SQL语言注册悬浮提示
  monaco.languages.registerHoverProvider('sql', {
    provideHover: (model, position) => {
      if (model.getWordAtPosition(position) != null) {
        const word = model.getWordAtPosition(position).word;
        return hoverTips(hoverProviderData, word);
      }
    },
  });
};
