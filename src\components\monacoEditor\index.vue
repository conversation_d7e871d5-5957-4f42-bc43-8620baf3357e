<template>
  <div class="box-body" :style="'height:' + editorConfig.ui.height + 'px;'">
    <div class="time">
      <div class="left-controls">
        <el-tooltip
          v-if="editorConfig.ui.needTime"
          content="插入当前时间"
          placement="bottom"
          effect="light"
        >
          <el-icon class="icon fa" @click="range(editor, '123')">
            <Timer />
          </el-icon>
        </el-tooltip>
        <el-tooltip
          v-if="editorConfig.ui.needTips"
          content="添加注释"
          placement="bottom"
          effect="light"
        >
          <el-icon class="icon fa" @click="tips(editor)">
            <SemiSelect />
          </el-icon>
        </el-tooltip>
      </div>

      <div class="editor-controls">
        <el-tooltip content="编辑器设置" placement="bottom" effect="light">
          <el-icon class="icon setting-icon" @click="openConfigDialog">
            <Setting />
          </el-icon>
        </el-tooltip>
      </div>
    </div>

    <div id="codeBox"></div>

    <!-- 编辑器配置对话框 -->
    <EditorConfigDialog
      v-model:visible="configDialogVisible"
      v-model:config="editorConfig"
      @insertExample="handleInsertExample"
    />

    <!-- 选中文本气泡 - 使用Teleport渲染到body -->
    <Teleport to="body">
      <SelectionBubble
        v-if="editorConfig.selectionBubble?.enabled"
        v-model:visible="bubbleVisible"
        :selectedText="bubbleData.selectedText"
        :selection="bubbleData.selection"
        :position="bubbleData.position"
        :editorContainer="bubbleData.editorContainer"
        :actions="getBubbleActions()"
        @action-click="handleBubbleActionClick"
        @hide="hideBubble"
      />
    </Teleport>
  </div>
</template>
<script setup>
  import { SemiSelect, Setting, Timer } from '@element-plus/icons-vue';
  import * as monaco from 'monaco-editor';
  import { onBeforeUnmount, onMounted, ref, toRaw, watch, Teleport } from 'vue';
  import { defaultEditorConfig, registerLanguageProviders } from './config.js';
  import EditorConfigDialog from './EditorConfigDialog.vue';
  import SelectionBubble from './SelectionBubble.vue';
  import vCompletion from './sql.js';
  import { range, tips } from './util.js';
  import { setupCustomContextMenu, hideDefaultContextMenu } from './contextMenu.js';
  import { setupJavaCodeCompletion, setupSQLCodeCompletion } from './completion.js';
  import { setupSelectionBubble, handleBubbleAction } from './selectionBubble.js';

  const emit = defineEmits([
    'contentChange',
    'update:modelValue',
    'writeToField',
    'formatError',
    'formatSuccess',
  ]);

  const props = defineProps({
    value: { type: String, default: '' },
    modelValue: { type: String, default: '' }, // 支持v-model
  });

  const editor = ref(null);
  const provider = ref(null);
  const myLang = ref(null);
  const color = ref(null);
  const hover = ref(null);

  // 配置对话框相关
  const configDialogVisible = ref(false);

  const editorConfig = ref({
    ...defaultEditorConfig,
  });

  // 选中文本气泡相关
  const bubbleVisible = ref(false);
  const bubbleData = ref({
    selectedText: '',
    selection: null,
    position: null,
    editorContainer: null,
  });
  let selectionBubbleCleanup = null;

  // 打开配置对话框
  const openConfigDialog = () => {
    configDialogVisible.value = true;
  };

  // 处理插入示例代码
  const handleInsertExample = (exampleCode) => {
    if (editor.value) {
      const position = toRaw(editor.value).getPosition();
      editor.value.executeEdits('insert-example', [
        {
          range: new monaco.Range(
            position.lineNumber,
            position.column,
            position.lineNumber,
            position.column,
          ),
          text: exampleCode,
        },
      ]);
      // 将光标移动到插入内容的末尾
      const lines = exampleCode.split('\n');
      const endLine = position.lineNumber + lines.length - 1;
      const endColumn =
        lines.length === 1
          ? position.column + exampleCode.length
          : lines[lines.length - 1].length + 1;
      toRaw(editor.value).setPosition({ lineNumber: endLine, column: endColumn });
      editor.value.focus();
    }
  };

  // 获取气泡操作按钮配置
  const getBubbleActions = () => {
    const config = editorConfig.value.selectionBubble || {};
    const actions = [];

    if (config.enableCopy) {
      actions.push({
        id: 'copy',
        label: '复制',
        type: 'primary',
        icon: 'DocumentCopy',
      });
    }

    if (config.enableCut) {
      actions.push({
        id: 'cut',
        label: '剪切',
        type: 'warning',
        icon: 'Scissors',
      });
    }

    console.log('🎯 [Editor] 生成气泡操作按钮:', actions);
    return actions;
  };

  // 显示选中文本气泡
  const showBubble = (data) => {
    console.log('🎈 [Editor] 显示气泡:', data);
    bubbleData.value = data;
    bubbleVisible.value = true;
  };

  // 隐藏选中文本气泡
  const hideBubble = () => {
    console.log('🫥 [Editor] 隐藏气泡');
    bubbleVisible.value = false;
    bubbleData.value = {
      selectedText: '',
      selection: null,
      position: null,
      editorContainer: null,
    };
  };

  // 处理气泡操作
  const handleBubbleActionClick = (actionData) => {
    console.log('🎯 [Editor] 处理气泡操作:', actionData);

    try {
      // 使用导入的handleBubbleAction函数
      handleBubbleAction(actionData, editor.value, emit);

      // 气泡会自动隐藏，不需要手动调用
      console.log('✅ [Editor] 气泡操作完成');
    } catch (error) {
      console.error('❌ [Editor] 气泡操作失败:', error);
    }
  };

  // 更新编辑器配置
  const updateEditorConfig = (newConfig) => {
    console.log('更新编辑器配置:', newConfig);

    if (!editor.value) {
      // 如果编辑器还未创建，直接更新配置
      Object.assign(editorConfig.value, newConfig);
      return;
    }

    // 保存当前状态
    const currentValue = toRaw(editor.value).getValue();
    const currentPosition = toRaw(editor.value).getPosition();
    const currentSelection = toRaw(editor.value).getSelection();

    // 检查哪些配置需要重新创建编辑器
    const needsRecreate =
      newConfig.theme !== editorConfig.value.theme ||
      newConfig.ui?.height !== editorConfig.value.ui?.height ||
      newConfig.ui?.disabled !== editorConfig.value.ui?.disabled ||
      newConfig.ui?.showMinimap !== editorConfig.value.ui?.showMinimap;

    // 更新配置
    Object.assign(editorConfig.value, newConfig);

    if (needsRecreate) {
      console.log('重新创建编辑器实例以应用新配置');

      // 销毁旧实例
      editor.value.dispose();

      // 创建新实例
      createEditorInstance();

      // 恢复内容和状态
      if (currentValue) {
        toRaw(editor.value).setValue(currentValue);
      }

      // 尝试恢复光标位置
      try {
        if (currentPosition) {
          toRaw(editor.value).setPosition(currentPosition);
        }
        if (currentSelection) {
          editor.value.setSelection(currentSelection);
        }
      } catch (e) {
        console.warn('无法恢复光标位置:', e);
      }
    } else {
      // 只更新可以动态更改的配置
      if (newConfig.language && newConfig.language !== editor.value.getModel().getLanguageId()) {
        monaco.editor.setModelLanguage(editor.value.getModel(), newConfig.language);
      }

      if (newConfig.fontSize) {
        editor.value.updateOptions({ fontSize: newConfig.fontSize });
      }

      console.log('动态更新编辑器配置完成');
    }
  };

  // 创建编辑器实例
  const createEditorInstance = () => {
    // 合并默认配置和用户配置
    const mergedConfig = {
      ...editorConfig.value,
      value: props.modelValue || props.value, // 优先使用modelValue支持v-model
      readOnly: editorConfig.value.ui?.disabled || false, // 使用config中的disabled配置
      minimap: {
        ...editorConfig.value.minimap,
        enabled: editorConfig.value.ui?.showMinimap || false, // 使用config中的showMinimap配置
      },
    };

    editor.value = monaco.editor.create(document.getElementById('codeBox'), mergedConfig);

    // 根据 config 是否开启自定义右键菜单
    const shouldEnableCustomMenu = editorConfig.value.contextMenu?.enableCustomMenu;
    if (shouldEnableCustomMenu) {
      const customMenuOptions = {
        enableCDataPath: editorConfig.value.contextMenu?.enableCDataPath !== false,
        enableRowTag: editorConfig.value.contextMenu?.enableRowTag !== false,
        enableFormatter: editorConfig.value.contextMenu?.enableFormatter !== false,
      };
      setupCustomContextMenu(editor.value, emit, customMenuOptions);
    }

    // 监听内容变化
    editor.value.onDidChangeModelContent(() => {
      const content = toRaw(editor.value).getValue();
      emit('contentChange', content);
      emit('update:modelValue', content); // 支持v-model
    });

    // 设置选中文本气泡功能
    console.log('🔧 [Editor] 检查气泡配置:', editorConfig.value.selectionBubble);

    if (editorConfig.value.selectionBubble?.enabled) {
      console.log('✅ [Editor] 启用选中文本气泡功能');

      const bubbleOptions = {
        minSelectionLength: editorConfig.value.selectionBubble.minSelectionLength,
        maxSelectionLength: editorConfig.value.selectionBubble.maxSelectionLength,
        showDelay: editorConfig.value.selectionBubble.showDelay,
        hideDelay: editorConfig.value.selectionBubble.hideDelay,
        enableForReadOnly: editorConfig.value.selectionBubble.enableForReadOnly,
      };

      console.log('⚙️ [Editor] 气泡选项:', bubbleOptions);

      selectionBubbleCleanup = setupSelectionBubble(
        editor.value,
        showBubble,
        hideBubble,
        bubbleOptions,
      );

      console.log('🎯 [Editor] 气泡功能设置完成');
    } else {
      console.log('❌ [Editor] 气泡功能未启用');
    }
  };

  // 初始化编辑器
  const initEditor = () => {
    registerLanguageProviders(monaco, provider, myLang, color, hover, vCompletion);

    // 设置Java和SQL代码补全
    setupJavaCodeCompletion();
    setupSQLCodeCompletion();

    createEditorInstance();
    // 根据 config 是否隐藏默认右键菜单
    const shouldHideDefaultMenu = editorConfig.value.contextMenu?.hideDefaultMenu;
    if (shouldHideDefaultMenu) {
      const hideMenuOptions = {
        hideAllDefault: editorConfig.value.contextMenu?.hideAllDefault !== false,
        customLabels: editorConfig.value.contextMenu?.customLabels || [
          '写到 CDataPath',
          '写到 RowTag',
          '格式化代码',
        ],
      };
      hideDefaultContextMenu(editor.value, hideMenuOptions);
    }
  };

  onMounted(() => {
    initEditor();
  });

  // 组件销毁前清理资源
  onBeforeUnmount(() => {
    // 清理选中文本气泡
    if (selectionBubbleCleanup) {
      selectionBubbleCleanup();
    }

    if (editor.value) {
      // 销毁编辑器实例
      toRaw(editor.value).dispose();
    }

    // 清理语言提供者
    if (provider.value) provider.value.dispose();
    if (myLang.value) myLang.value.dispose();
    if (color.value) color.value.dispose();
    if (hover.value) hover.value.dispose();
  });

  // 统一监听value和modelValue变化
  watch(
    [() => props.value, () => props.modelValue],
    ([newValue, newModelValue]) => {
      if (editor.value) {
        const currentValue = toRaw(editor.value).getValue();
        // 优先使用modelValue，如果modelValue不存在则使用value
        const valueToUse = newModelValue !== undefined ? newModelValue : newValue;

        // 避免重复设置相同的值
        if (valueToUse !== currentValue) {
          toRaw(editor.value).setValue(valueToUse);
        }
      }
    },
    { deep: true },
  );

  // 监听disabled属性变化
  watch(
    () => props.disabled,
    (newVal) => {
      if (editor.value) {
        toRaw(editor.value).updateOptions({ readOnly: newVal });
        editorConfig.value.readOnly = newVal; // 同步配置对象
      }
    },
  );

  // 监听showMinimap属性变化
  watch(
    () => props.showMinimap,
    (newVal) => {
      if (editor.value) {
        toRaw(editor.value).updateOptions({ minimap: { enabled: newVal } });
        editorConfig.value.minimap.enabled = newVal; // 同步配置对象
      }
    },
  );

  // 监听配置变更
  watch(
    () => editorConfig.value,
    (newConfig) => {
      if (editor.value) {
        // 更新编辑器配置
        toRaw(editor.value).updateOptions(newConfig);

        // 更新编辑器模型的语言
        if (newConfig.language) {
          monaco.editor.setModelLanguage(toRaw(editor.value).getModel(), newConfig.language);
        }

        // 配置已更新
        console.log('编辑器配置已更新:', newConfig.language);
      }
    },
    { deep: true },
  );

  // 监听config中language属性变化
  watch(
    () => editorConfig.value.language,
    (newLanguage, oldLanguage) => {
      console.log('语言配置变化:', oldLanguage, '->', newLanguage);

      if (editor.value) {
        try {
          const model = toRaw(editor.value).getModel();
          console.log('当前模型语言:', model.getLanguageId());

          // 更新编辑器模型的语言
          monaco.editor.setModelLanguage(model, newLanguage);

          // 验证语言是否更新成功
          console.log('更新后模型语言:', model.getLanguageId());

          console.log('语言切换完成:', newLanguage);
        } catch (error) {
          console.error('语言切换失败:', error);
        }
      } else {
        console.warn('编辑器实例不存在，无法切换语言');
      }
    },
  );

  // 暴露方法给父组件
  defineExpose({
    editor,
    editorConfig,
    updateEditorConfig,
  });
</script>
<style scoped>
  #codeBox {
    width: 100%;
    height: calc(100% - 40px);
    /* 固定高度计算，减去顶部控制栏高度 */
  }

  .time {
    width: 100%;
    border: 1px solid #ccc;
    border-bottom: none;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    height: 40px;
    /* 固定高度 */
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .left-controls {
    display: flex;
    align-items: center;
    margin-left: 15px;
  }

  .icon {
    display: inline-block;
    font-size: 20px !important;
    margin: 0 10px;
  }

  .icon:hover {
    color: #ffa500;
    font-weight: bold;
  }

  .editor-controls {
    display: flex;
    align-items: center;
    margin-right: 15px;
    gap: 15px;
  }

  .control-item {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .control-label {
    font-size: 12px;
    color: #606266;
  }

  .setting-icon {
    cursor: pointer;
    font-size: 18px;
    color: #409eff;
    transition: transform 0.3s ease;
  }

  .setting-icon:hover {
    transform: rotate(30deg);
    color: #66b1ff;
  }
</style>
