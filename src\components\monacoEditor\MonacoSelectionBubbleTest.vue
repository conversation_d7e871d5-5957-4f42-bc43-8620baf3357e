<template>
  <div class="monaco-selection-bubble-test">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>Monaco编辑器选中文本气泡测试</span>
          <el-button type="primary" @click="openDevTools">打开开发者工具</el-button>
        </div>
      </template>

      <div class="test-content">
        <el-alert
          title="测试说明"
          type="info"
          :closable="false"
          show-icon
        >
          <p>1. 在下面的编辑器中选中任意文本</p>
          <p>2. 观察是否弹出操作气泡</p>
          <p>3. 查看浏览器控制台的调试日志</p>
          <p>4. 点击气泡中的操作按钮测试功能</p>
        </el-alert>

        <div class="editor-section">
          <h3>Monaco编辑器</h3>
          <div class="editor-wrapper">
            <MonacoEditor
              ref="editorRef"
              v-model="testContent"
              @bubbleAction="handleBubbleAction"
              @replaceText="handleReplaceText"
            />
          </div>
        </div>

        <div class="status-section">
          <h3>状态信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="气泡状态">
              <el-tag :type="bubbleEnabled ? 'success' : 'danger'">
                {{ bubbleEnabled ? '已启用' : '未启用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="最后操作">
              {{ lastAction || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="选中文本">
              {{ lastSelectedText || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="操作时间">
              {{ lastActionTime || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="log-section">
          <h3>操作日志</h3>
          <div class="log-container">
            <div v-if="logs.length === 0" class="no-logs">
              暂无日志，请在编辑器中选中文本进行测试
            </div>
            <div v-for="(log, index) in logs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-type" :class="log.type">{{ log.type }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
          <el-button @click="clearLogs" size="small" type="warning" style="margin-top: 10px;">
            清空日志
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import MonacoEditor from '@/components/monacoEditor/index.vue';

  // 测试内容
  const testContent = ref(`<?xml version="1.0" encoding="UTF-8"?>
<root>
  <data>
    <item id="1">
      <name>测试数据</name>
      <value>这是一段测试文本，请选中这些文字来测试气泡功能</value>
      <description>Monaco编辑器选中文本气泡功能测试</description>
    </item>
    <item id="2">
      <name>示例内容</name>
      <value>你可以选中任意文本来触发气泡显示</value>
      <tags>
        <tag>JavaScript</tag>
        <tag>TypeScript</tag>
        <tag>Vue.js</tag>
        <tag>Monaco Editor</tag>
      </tags>
    </item>
  </data>
  <config>
    <settings>
      <bubble enabled="true" />
      <actions copy="true" search="true" translate="true" />
    </settings>
  </config>
</root>

// 这里是一些JavaScript代码
function testFunction() {
  console.log('这是一个测试函数');
  const message = '选中这段代码试试看';
  return message;
}

// CSS样式示例
.test-class {
  color: #409eff;
  font-size: 14px;
  background-color: #f5f7fa;
}

/* 选中这些注释文本 */
/* 测试气泡功能是否正常工作 */`);

  // 编辑器引用
  const editorRef = ref(null);

  // 状态数据
  const bubbleEnabled = ref(true);
  const lastAction = ref('');
  const lastSelectedText = ref('');
  const lastActionTime = ref('');

  // 日志数据
  const logs = ref([]);

  // 添加日志
  const addLog = (type, message) => {
    const now = new Date();
    logs.value.unshift({
      time: now.toLocaleTimeString(),
      type,
      message,
    });
    
    // 限制日志数量
    if (logs.value.length > 100) {
      logs.value = logs.value.slice(0, 100);
    }
  };

  // 处理气泡操作
  const handleBubbleAction = (actionData) => {
    const { action, selectedText } = actionData;
    
    lastAction.value = action.label;
    lastSelectedText.value = selectedText;
    lastActionTime.value = new Date().toLocaleTimeString();
    
    addLog('ACTION', `执行操作: ${action.label} - "${selectedText.substring(0, 20)}${selectedText.length > 20 ? '...' : ''}"`);
    
    console.log('🎯 [Test] 气泡操作:', actionData);
  };

  // 处理文本替换
  const handleReplaceText = (data) => {
    addLog('REPLACE', `替换文本请求: "${data.selectedText.substring(0, 20)}${data.selectedText.length > 20 ? '...' : ''}"`);
    console.log('🔄 [Test] 替换文本:', data);
  };

  // 打开开发者工具提示
  const openDevTools = () => {
    addLog('INFO', '请按F12打开开发者工具查看控制台日志');
    alert('请按F12打开开发者工具，然后查看Console标签页的调试日志');
  };

  // 清空日志
  const clearLogs = () => {
    logs.value = [];
    addLog('INFO', '日志已清空');
  };

  // 组件挂载时的初始化
  onMounted(() => {
    addLog('INFO', '选中文本气泡测试页面已加载');
    addLog('INFO', '请在编辑器中选中文本进行测试');
    
    // 拦截控制台日志
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;
    
    console.log = function(...args) {
      originalLog.apply(console, args);
      const message = args.join(' ');
      if (message.includes('[SelectionBubble]') || message.includes('[Editor]')) {
        addLog('DEBUG', message);
      }
    };
    
    console.warn = function(...args) {
      originalWarn.apply(console, args);
      const message = args.join(' ');
      if (message.includes('[SelectionBubble]') || message.includes('[Editor]')) {
        addLog('WARN', message);
      }
    };
    
    console.error = function(...args) {
      originalError.apply(console, args);
      const message = args.join(' ');
      if (message.includes('[SelectionBubble]') || message.includes('[Editor]')) {
        addLog('ERROR', message);
      }
    };
  });
</script>

<style scoped>
  .monaco-selection-bubble-test {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .test-card {
    margin-bottom: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .test-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .editor-section h3,
  .status-section h3,
  .log-section h3 {
    margin-bottom: 10px;
    color: #409eff;
  }

  .editor-wrapper {
    height: 400px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    overflow: hidden;
  }

  .log-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
    background-color: #fafafa;
    font-family: 'Courier New', monospace;
    font-size: 12px;
  }

  .no-logs {
    text-align: center;
    color: #909399;
    padding: 20px;
  }

  .log-item {
    display: flex;
    padding: 4px 0;
    border-bottom: 1px solid #e4e7ed;
  }

  .log-item:last-child {
    border-bottom: none;
  }

  .log-time {
    color: #909399;
    width: 80px;
    flex-shrink: 0;
  }

  .log-type {
    width: 80px;
    flex-shrink: 0;
    font-weight: bold;
  }

  .log-type.INFO {
    color: #409eff;
  }

  .log-type.DEBUG {
    color: #909399;
  }

  .log-type.ACTION {
    color: #67c23a;
  }

  .log-type.REPLACE {
    color: #e6a23c;
  }

  .log-type.WARN {
    color: #e6a23c;
  }

  .log-type.ERROR {
    color: #f56c6c;
  }

  .log-message {
    color: #606266;
    flex: 1;
    word-break: break-all;
  }
</style>
