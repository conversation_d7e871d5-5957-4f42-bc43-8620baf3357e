<template>
  <div class="form-design-container">
    <!-- 视图选择区域 -->
    <div class="form-selector">
      <el-form :inline="true" class="selector-form">
        <el-form-item label="选择视图：">
          <el-select
            v-model="selectedViews"
            placeholder="请选择视图"
            multiple
            @change="handleViewChange"
            style="width: 300px"
          >
            <el-option
              v-for="item in availableViews"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-buttons" v-if="viewList.length > 0">
      <el-button @click="validateAllForms" type="primary" icon="Check">
        校验所有表单
      </el-button>
      <el-button @click="handleSubmit" type="success" icon="Upload">
        提交表单
      </el-button>
      <el-button @click="resetAllForms" type="warning" icon="RefreshLeft">
        重置所有表单
      </el-button>
      <el-button @click="saveAllConfigs" type="info" icon="Document">
        保存配置
      </el-button>
    </div>

    <!-- 视图设计区域 -->
    <div class="design-area" v-if="viewList.length > 0">
      <el-tabs
        v-model="activeName"
        type="card"
        editable
        @edit="handleTabEdit"
        @tab-click="handleTabClick"
      >
        <el-tab-pane
          v-for="item in viewList"
          :key="item.id"
          :label="item.name"
          :name="item.id"
        >
          <!-- 在这里放置表单设计器 -->
          <ng-form-design
            :ref="el => setFormDesignRef(el, item.id)"
            :key="item.id"
            :view-id="item.id"
            @on-change="(config) => handleFormConfigChange(config, item.id)"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 空状态提示 -->
    <div v-else class="empty-state">
      <el-empty description="请先选择视图开始设计" />
    </div>
  </div>
</template>

<script setup>
import NgFormDesign from '@/views/ngForm/packages/form-design/index.vue'
import { ref, reactive, computed, onMounted, provide, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { viewOptions, fieldOptions } from '@/views/masterData/masterDataCoding/mockdata.js'

// 字段选择功能相关的响应式数据 - 改为基于viewId的键值对结构
const enableFieldSelect = ref(false)
const fieldOptionsMap = reactive({}) // 存储每个viewId对应的字段选项
const currentViewId = ref('')

// 为 NgFormDesign 组件提供所需的注入
provide('$ngofrm_components', [])
provide('$config', {
  $ngofrm_components: [],
  $ngofrm_config: {}
})

// 提供字段选择功能的依赖注入 - 使用新的数据结构
provide('enableFieldSelect', enableFieldSelect)
provide('fieldOptionsMap', fieldOptionsMap)
provide('currentViewId', currentViewId)

// 响应式数据
const selectedViews = ref([])
const activeName = ref('')
const formDesignRefs = ref(new Map())

// 默认表单ID为客户主数据模型
const formId = 'customer_model'

// 可用视图选项（固定为客户主数据模型的视图）
const availableViews = computed(() => {
  return viewOptions[formId] || []
})

// 已选择的视图列表
const viewList = computed(() => {
  return selectedViews.value.map(viewValue => {
    const viewOption = availableViews.value.find(v => v.value === viewValue)
    return {
      id: viewValue,
      name: viewOption?.label || viewValue,
      value: viewValue
    }
  })
})

// 表单配置存储（每个视图对应一个配置）
const formConfigs = reactive({})

// 更新字段选项数据 - 改为基于viewId的键值对存储
const updateFieldOptions = (viewId) => {
  console.log('updateFieldOptions 被调用, viewId:', viewId)
  console.log('当前 fieldOptionsMap:', fieldOptionsMap)

  // 获取字段选项
  let options = fieldOptions[viewId] || []

  if (options.length === 0) {
    console.log('使用默认字段选项')
    options = [
      { label: '客户编号(customer_code)', value: 'customer_code' },
      { label: '客户名称(customer_name)', value: 'customer_name' },
      { label: '客户类型(customer_type)', value: 'customer_type' },
      { label: '联系人(contact_person)', value: 'contact_person' },
      { label: '联系电话(contact_phone)', value: 'contact_phone' },
      { label: '邮箱地址(email)', value: 'email' },
      { label: '注册地址(address)', value: 'address' },
      { label: '创建时间(create_time)', value: 'create_time' },
      { label: '更新时间(update_time)', value: 'update_time' },
      { label: '状态(status)', value: 'status' },
      { label: '备注(remark)', value: 'remark' }
    ]
  }

  console.log('设置字段选项 for viewId:', viewId, options)
  // 将字段选项存储到对应的viewId键下 - 总是更新以确保响应式
  fieldOptionsMap[viewId] = options

  console.log('更新后的 fieldOptionsMap:', fieldOptionsMap)
}



// 设置表单设计器引用
const setFormDesignRef = (el, viewId) => {
  if (el) {
    formDesignRefs.value.set(viewId, el)
  }
}

// 处理视图选择变化
const handleViewChange = (newSelectedViews) => {
  console.log('选择的视图:', newSelectedViews)

  // 如果有新增的视图，设置第一个为激活状态
  if (newSelectedViews.length > 0 && !activeName.value) {
    activeName.value = newSelectedViews[0]
  }

  // 移除未选择视图的配置和字段选项
  Object.keys(formConfigs).forEach(viewId => {
    if (!newSelectedViews.includes(viewId)) {
      delete formConfigs[viewId]
      // 同时删除对应的字段选项
      delete fieldOptionsMap[viewId]
    }
  })

  // 启用字段选择功能并设置当前视图ID
  if (newSelectedViews.length > 0) {
    console.log('启用字段选择功能，选择的视图:', newSelectedViews);

    const viewId = activeName.value || newSelectedViews[0]

    // 更新依赖注入的数据
    enableFieldSelect.value = true
    currentViewId.value = viewId

    // 为每个选中的视图初始化字段选项
    newSelectedViews.forEach(vid => {
      updateFieldOptions(vid)
    })
  } else {
    console.log('禁用字段选择功能')
    // 禁用字段选择功能
    enableFieldSelect.value = false
    currentViewId.value = ''
    // 清空字段选项映射
    Object.keys(fieldOptionsMap).forEach(key => {
      delete fieldOptionsMap[key]
    })
  }
}

// 处理tab编辑（删除）
const handleTabEdit = (targetName, action) => {
  if (action === 'remove') {
    // 从选择的视图中移除
    const index = selectedViews.value.indexOf(targetName)
    if (index > -1) {
      selectedViews.value.splice(index, 1)
    }

    // 删除对应的表单配置和字段选项
    delete formConfigs[targetName]
    delete fieldOptionsMap[targetName]

    // 如果删除的是当前激活的tab，切换到其他tab
    if (activeName.value === targetName) {
      const newActiveTab = selectedViews.value.length > 0 ? selectedViews.value[0] : ''
      activeName.value = newActiveTab
      // 更新当前视图ID
      if (newActiveTab) {
        currentViewId.value = newActiveTab
        updateFieldOptions(newActiveTab)
      } else {
        currentViewId.value = ''
      }
    }


  }
}

// 处理tab点击
const handleTabClick = (tab) => {
  console.log('=== 切换到tab ===')
  console.log('tab.name:', tab.name)
  console.log('切换前 currentViewId:', currentViewId.value)
  console.log('切换前 fieldOptionsMap:', fieldOptionsMap)

  // 更新当前视图ID
  currentViewId.value = tab.name
  console.log('切换后 currentViewId:', currentViewId.value)

  // 确保该视图的字段选项已初始化
  updateFieldOptions(tab.name)

  console.log('=== tab切换完成 ===')
}

// 处理表单配置变化
const handleFormConfigChange = (formConfig, viewId) => {
  console.log(`视图 ${viewId} 的表单配置已更改:`, formConfig)
  formConfigs[viewId] = formConfig
  // 这里可以添加自动保存逻辑
}

// 获取指定视图的表单配置
const getFormConfig = (viewId) => {
  if (viewId) {
    const ref = formDesignRefs.value.get(viewId)
    if (ref) {
      return ref.getFormConfig()
    }
  }
  return null
}

// 获取所有视图的表单配置
const getAllFormConfigs = () => {
  const configs = {}
  formDesignRefs.value.forEach((ref, viewId) => {
    if (ref) {
      configs[viewId] = ref.getFormConfig()
    }
  })
  return configs
}

// 设置指定视图的表单配置
const setFormConfig = (viewId, config) => {
  const ref = formDesignRefs.value.get(viewId)
  if (ref) {
    ref.setFormConfig(config)
  }
}

// 保存所有表单配置
const saveAllConfigs = () => {
  const allConfigs = getAllFormConfigs()
  console.log('保存所有表单配置:', allConfigs)

  // 这里可以调用API保存配置
  ElMessage.success('表单配置保存成功')

  return allConfigs
}

// 获取指定表单的 ref
const getFormRef = (viewId) => {
  const formRef = formDesignRefs.value.get(viewId)
  if (!formRef) {
    console.error(`❌ 未找到表单 ${viewId} 的 ref`)
    return null
  }
  return formRef
}

// 校验单个表单
const validateSingleForm = async (viewId) => {
  const formRef = getFormRef(viewId)
  if (!formRef) {
    return {
      valid: false,
      viewId,
      error: `表单 ${viewId} 的 ref 未找到`
    }
  }

  try {
    // 检查表单组件是否有 validate 方法
    if (typeof formRef.validate !== 'function') {
      console.error(`❌ 表单 ${viewId} 没有 validate 方法`)
      return {
        valid: false,
        viewId,
        error: `表单 ${viewId} 没有 validate 方法`
      }
    }

    console.log(`🔍 开始校验表单 ${viewId}...`)

    // 如果不是当前激活的标签页，需要先切换到该标签页
    const originalActiveTab = activeName.value
    const needSwitchTab = activeName.value !== viewId

    if (needSwitchTab) {
      console.log(`🔄 切换到标签页 ${viewId} 进行校验`)
      activeName.value = viewId
      currentViewId.value = viewId

      // 等待DOM更新
      await nextTick()

      // 再次获取ref，确保获取到正确的组件实例
      const updatedFormRef = getFormRef(viewId)
      if (!updatedFormRef) {
        return {
          valid: false,
          viewId,
          error: `切换标签页后未找到表单 ${viewId} 的 ref`
        }
      }
    }

    // 调用表单的 validate 方法
    const currentFormRef = getFormRef(viewId)
    const isValid = await currentFormRef.validate()

    console.log(`✅ 表单 ${viewId} 校验结果:`, isValid)

    // 如果切换了标签页，校验完成后恢复原来的标签页
    if (needSwitchTab && originalActiveTab) {
      console.log(`🔄 恢复到原标签页 ${originalActiveTab}`)
      activeName.value = originalActiveTab
      currentViewId.value = originalActiveTab
      await nextTick()
    }

    return {
      valid: isValid,
      viewId,
      error: null
    }
  } catch (error) {
    console.error(`❌ 表单 ${viewId} 校验失败:`, error)
    return {
      valid: false,
      viewId,
      error: error.message || '校验过程中发生错误'
    }
  }
}

// 校验所有表单
const validateAllForms = async () => {
  try {
    console.log('🔍 开始校验所有表单...')

    // 获取所有表单ID
    const viewIds = Array.from(formDesignRefs.value.keys())

    if (viewIds.length === 0) {
      ElMessage.warning('没有找到需要校验的表单！')
      return false
    }

    console.log('📋 需要校验的表单:', viewIds)

    // 记录原始激活的标签页
    const originalActiveTab = activeName.value

    // 按顺序校验每个表单，避免并发导致的标签页切换问题
    const results = []
    for (const viewId of viewIds) {
      console.log(`🔍 正在校验表单: ${viewId}`)
      const result = await validateSingleForm(viewId)
      results.push(result)

      // 如果校验失败，记录详细信息
      if (!result.valid) {
        console.error(`❌ 表单 ${viewId} 校验失败:`, result.error)
      } else {
        console.log(`✅ 表单 ${viewId} 校验通过`)
      }
    }

    // 恢复到原始标签页
    if (originalActiveTab && activeName.value !== originalActiveTab) {
      console.log(`🔄 恢复到原始标签页: ${originalActiveTab}`)
      activeName.value = originalActiveTab
      currentViewId.value = originalActiveTab
      await nextTick()
    }

    // 统计校验结果
    let allValid = true
    const failedForms = []

    results.forEach(result => {
      if (!result.valid) {
        allValid = false
        failedForms.push({
          viewId: result.viewId,
          error: result.error
        })
      }
    })

    if (allValid) {
      ElMessage.success('所有表单校验通过！')
      return true
    } else {
      console.error('校验失败的表单:', failedForms)
      ElMessage.error(`有 ${failedForms.length} 个表单校验失败！`)

      // 显示详细错误信息，包含表单名称
      const errorMessages = failedForms.map(f => {
        const viewOption = availableViews.value.find(v => v.value === f.viewId)
        const viewName = viewOption?.label || f.viewId
        return `表单 ${viewName}: ${f.error}`
      }).join('\n')

      ElMessageBox.alert(errorMessages, '表单校验失败', {
        confirmButtonText: '确定',
        type: 'error',
      })

      // 跳转到第一个校验失败的表单
      if (failedForms.length > 0) {
        const firstFailedForm = failedForms[0].viewId
        console.log(`🔄 跳转到第一个校验失败的表单: ${firstFailedForm}`)
        activeName.value = firstFailedForm
        currentViewId.value = firstFailedForm
      }

      return false
    }
  } catch (error) {
    console.error('校验所有表单时发生错误:', error)
    ElMessage.error('校验过程中发生错误！')
    return false
  }
}

// 获取所有表单数据（带校验）
const getAllFormsData = async () => {
  try {
    const viewIds = Array.from(formDesignRefs.value.keys())

    if (viewIds.length === 0) {
      return {}
    }

    console.log('📋 开始获取所有表单数据:', viewIds)

    // 记录原始激活的标签页
    const originalActiveTab = activeName.value

    // 按顺序获取每个表单的数据
    const results = []
    for (const viewId of viewIds) {
      console.log(`📄 正在获取表单 ${viewId} 的数据`)

      // 切换到对应的标签页
      if (activeName.value !== viewId) {
        console.log(`🔄 切换到标签页 ${viewId}`)
        activeName.value = viewId
        currentViewId.value = viewId
        await nextTick()
      }

      const formRef = getFormRef(viewId)
      if (formRef && typeof formRef.getData === 'function') {
        try {
          // getData(false) 会先校验再获取数据
          const data = await formRef.getData(false)
          results.push({
            viewId,
            data,
            success: true
          })
          console.log(`✅ 表单 ${viewId} 数据获取成功`)
        } catch (error) {
          console.error(`❌ 表单 ${viewId} 数据获取失败:`, error)
          results.push({
            viewId,
            error: error.message || '获取数据失败',
            success: false
          })
        }
      } else {
        console.error(`❌ 表单 ${viewId} 组件未找到或没有 getData 方法`)
        results.push({
          viewId,
          error: '表单组件未找到或没有 getData 方法',
          success: false
        })
      }
    }

    // 恢复到原始标签页
    if (originalActiveTab && activeName.value !== originalActiveTab) {
      console.log(`🔄 恢复到原始标签页: ${originalActiveTab}`)
      activeName.value = originalActiveTab
      currentViewId.value = originalActiveTab
      await nextTick()
    }

    // 检查是否所有表单数据都获取成功
    const failedData = results.filter(result => !result.success)
    if (failedData.length > 0) {
      console.error('获取表单数据失败:', failedData)

      // 显示详细错误信息
      const errorMessages = failedData.map(f => {
        const viewOption = availableViews.value.find(v => v.value === f.viewId)
        const viewName = viewOption?.label || f.viewId
        return `表单 ${viewName}: ${f.error}`
      }).join('\n')

      throw new Error(`获取表单数据失败:\n${errorMessages}`)
    }

    // 构建返回数据
    const allData = {}
    results.forEach(result => {
      allData[result.viewId] = result.data
    })

    console.log('✅ 所有表单数据获取完成:', allData)
    return allData
  } catch (error) {
    console.error('获取所有表单数据时发生错误:', error)
    throw error
  }
}

// 提交前的完整校验和数据获取
const handleSubmit = async () => {
  try {
    console.log('📤 开始提交流程...')

    // 1. 先校验所有表单
    const allValid = await validateAllForms()
    if (!allValid) {
      ElMessage.warning('请先修复表单校验错误！')
      return
    }

    // 2. 获取所有表单数据
    const allFormsData = await getAllFormsData()
    console.log('📋 所有表单数据:', allFormsData)

    // 3. 确认提交
    await ElMessageBox.confirm(
      '所有表单校验通过，确定要提交吗？',
      '确认提交',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 4. 调用提交接口
    await submitToServer(allFormsData)

    ElMessage.success('提交成功！')

  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交时发生错误:', error)
      ElMessage.error('提交失败：' + (error.message || '未知错误'))
    }
  }
}

// 重置所有表单
const resetAllForms = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置所有表单吗？这将清空所有已填写的内容。',
      '确认重置',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const viewIds = Array.from(formDesignRefs.value.keys())

    viewIds.forEach(viewId => {
      const formRef = getFormRef(viewId)
      if (formRef && typeof formRef.reset === 'function') {
        formRef.reset()
      }
    })

    ElMessage.success('所有表单重置成功！')

  } catch (error) {
    if (error !== 'cancel') {
      console.error('重置表单时发生错误:', error)
      ElMessage.error('重置失败！')
    }
  }
}

// 提交到服务器的函数（需要根据实际情况实现）
const submitToServer = async (formData) => {
  // 这里实现您的实际提交逻辑
  // 例如：
  // const response = await api.submitForms(formData);
  // return response;

  // 模拟提交
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log('模拟提交成功，数据:', formData)
      resolve()
    }, 1000)
  })
}




// 组件挂载时初始化
onMounted(() => {
  console.log('formDesign 组件已挂载')
  console.log('初始状态:')
  console.log('- selectedViews:', selectedViews.value)
  console.log('- enableFieldSelect:', enableFieldSelect.value)
  console.log('- currentViewId:', currentViewId.value)
  console.log('- fieldOptionsMap:', fieldOptionsMap)

  // 如果有默认视图，初始化字段选择功能
  if (selectedViews.value.length > 0) {
    const defaultViewId = selectedViews.value[0]
    enableFieldSelect.value = true
    currentViewId.value = defaultViewId
    updateFieldOptions(defaultViewId)

    console.log('初始化完成后:')
    console.log('- enableFieldSelect:', enableFieldSelect.value)
    console.log('- currentViewId:', currentViewId.value)
    console.log('- fieldOptionsMap:', fieldOptionsMap)
  }
})





// 暴露方法给父组件使用
defineExpose({
  getFormConfig,
  getAllFormConfigs,
  setFormConfig,
  saveAllConfigs,
  // 新增的校验相关方法
  validateSingleForm,
  validateAllForms,
  getAllFormsData,
  handleSubmit,
  resetAllForms,
  getFormRef
})
</script>

<style lang="scss" scoped>
.form-design-container {
  width: 100%;
  height: 100%;
  min-height: 600px;
  display: flex;
  flex-direction: column;

  .form-selector {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;

    .selector-form {
      margin: 0;

      .el-form-item {
        margin-bottom: 0;
        margin-right: 24px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .action-buttons {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;
    text-align: center;

    .el-button {
      margin: 0 8px;
    }
  }

  .design-area {
    flex: 1;
    min-height: 500px;

    .el-tabs {
      height: 100%;

      :deep(.el-tabs__content) {
        height: calc(100% - 40px);
        padding: 16px 0;
      }

      :deep(.el-tab-pane) {
        height: 100%;
      }
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
  }

  // 确保表单设计器占满容器
  :deep(.ng-form-design) {
    height: 100%;
  }
}
</style>