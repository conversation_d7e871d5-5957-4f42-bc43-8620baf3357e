import { toRaw } from 'vue';

/**
 * Monaco Editor 基础工具函数
 * 包含文本插入、注释添加等基础功能
 */

/**
 * 在编辑器中插入注释
 * @param {Object} v - Monaco编辑器实例
 */
export const tips = (v) => {
  range(
    v,
    `
  #--注释--#
        `,
  );
};

/**
 * 在编辑器当前光标位置插入文本
 * @param {Object} v - Monaco编辑器实例
 * @param {string} text - 要插入的文本
 */
export const range = (v, text) => {
  const position = toRaw(v).getPosition();
  toRaw(v).executeEdits('', [
    {
      range: {
        startLineNumber: position.lineNumber,
        startColumn: position.column,
        endLineNumber: position.lineNumber,
        endColumn: position.column,
      },
      text,
    },
  ]);
};